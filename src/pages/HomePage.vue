<script setup lang="ts">
import { useRouter } from 'vue-router';
import { useAuthStore } from '../stores/auth';
import type { User } from '../types/models';
import { ref, onMounted, computed } from 'vue';
import HomeCard from '../components/HomeCard.vue';
import { useGlobalStore } from '../stores/global';
import type { System } from '../types/app';

const authStore = useAuthStore();
const globalStore = useGlobalStore();
const router = useRouter();
const user = ref<User | undefined>(authStore.getCurrentUser());

const systems = computed(() => {
  return globalStore.getSystemsByRole(authStore.currentRoleName);
});
const onClickCard = async (system: System) => {
  await router.push({
    path: system.path,
  });
};

onMounted(() => {
  user.value = authStore.getCurrentUser();
});
</script>

<template>
  <q-page padding>
    <div class="q-mb-lg">
      <h1 class="text-h4 q-mb-md">สวัสดี, {{ user?.name }}!</h1>
      <p class="text-subtitle1 q-mb-md">กรุณาเลือกระบบเพื่อเริ่มใช้งาน</p>
    </div>
    <div class="row q-col-gutter-md justify-start">
      <div v-for="system in systems" :key="system.id">
        <HomeCard :system="system" @clickCard="onClickCard" />
      </div>
    </div>
  </q-page>
</template>
