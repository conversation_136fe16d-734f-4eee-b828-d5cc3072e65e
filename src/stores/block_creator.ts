import { defineStore } from 'pinia';
import type { ItemBlock, Assessment } from 'src/types/models';
import type { DataResponse } from 'src/types/data';
import { ref, computed, nextTick } from 'vue';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { getCurrentSection } from 'src/utils/block_helper';
import { useGlobalStore } from './global';

type DOMRefElement = Element | null;

// Allowed types for AnswerItemBlockType
const allowedTypes: string[] = ['RADIO', 'CHECKBOX', 'TEXTFIELD', 'GRID'];

export const useBlockCreatorStore = defineStore('blockCreator', () => {
  // State
  const blocks = ref<ItemBlock[]>([]);
  const blockRefs: Record<number, DOMRefElement> = {};
  const selectedBlock = ref<ItemBlock | null>(null);
  const selectedBlockId = ref<string | undefined>();

  // Assessment management state (migrated from form.ts)
  const meta = ref<DataResponse<Assessment> | null>(null);
  const assessments = ref<Assessment[]>([]);
  const currentAssessment = ref<Assessment | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);
  const page = ref(1);
  const limit = ref(5);
  const search = ref('');

  // Global state for duplicate dialog (migrated from formItemCreateStore.ts)
  const duplicateDialog = ref(false);

  // Block creation and UI state
  const isCreatingBlock = ref(false);
  const isDragging = ref(false);

  // FAB positioning state - consolidated for better performance
  const fabState = ref({
    positionLock: false,
    pendingPosition: null as number | null,
    creationInProgress: false,
    targetBlockId: null as number | null,
  });

  // ID generation utilities
  const lastUsedQuestionId = ref(0);
  const lastUsedOptionId = ref(0);

  function generateQuestionId(): number {
    lastUsedQuestionId.value++;
    return lastUsedQuestionId.value;
  }
  function generateOptionId(): number {
    lastUsedOptionId.value++;
    return lastUsedOptionId.value;
  }

  // Computed properties for FAB state management
  const fabPositionLock = computed({
    get: () => fabState.value.positionLock,
    set: (value: boolean) => {
      fabState.value.positionLock = value;
    },
  });

  const pendingFabPosition = computed({
    get: () => fabState.value.pendingPosition,
    set: (value: number | null) => {
      fabState.value.pendingPosition = value;
    },
  });

  const blockCreationInProgress = computed({
    get: () => fabState.value.creationInProgress,
    set: (value: boolean) => {
      fabState.value.creationInProgress = value;
    },
  });

  const targetBlockId = computed({
    get: () => fabState.value.targetBlockId,
    set: (value: number | null) => {
      fabState.value.targetBlockId = value;
    },
  });

  // Timeout management with cleanup
  const timeoutManager = {
    fabPositionTimeout: null as NodeJS.Timeout | null,
    lockReleaseTimeout: null as NodeJS.Timeout | null,

    clearFabTimeout() {
      if (this.fabPositionTimeout) {
        clearTimeout(this.fabPositionTimeout);
        this.fabPositionTimeout = null;
      }
    },

    clearLockTimeout() {
      if (this.lockReleaseTimeout) {
        clearTimeout(this.lockReleaseTimeout);
        this.lockReleaseTimeout = null;
      }
    },

    clearAll() {
      this.clearFabTimeout();
      this.clearLockTimeout();
    },
  };

  // FAB positioning with better performance
  const setFabPosition = (blockId: number, immediate = false) => {
    // Early return for block creation priority
    if (isCreatingBlock.value && !immediate) {
      pendingFabPosition.value = blockId;
      return;
    }

    // Clear existing timeouts
    timeoutManager.clearFabTimeout();

    if (immediate) {
      // Immediate positioning for critical operations
      fabPositionLock.value = true;
      selectedBlockId.value = `block-${blockId}`;

      // Clear any existing lock release timeout
      timeoutManager.clearLockTimeout();

      // Set new lock release timeout
      timeoutManager.lockReleaseTimeout = setTimeout(() => {
        fabPositionLock.value = false;
        // Apply any pending position change
        const pending = pendingFabPosition.value;
        if (pending && pending !== blockId) {
          setFabPosition(pending, false);
          pendingFabPosition.value = null;
        }
      }, 200);
    } else {
      // Debounced positioning for regular interactions
      timeoutManager.fabPositionTimeout = setTimeout(() => {
        if (!fabPositionLock.value) {
          selectedBlockId.value = `block-${blockId}`;
        } else {
          // Store as pending if locked
          pendingFabPosition.value = blockId;
        }
      }, 50);
    }
  };

  // Block operations
  function addBlock(item: ItemBlock, index: number) {
    blocks.value.splice(index + 1, 0, item);
  }

  function appendBlock(item: ItemBlock) {
    blocks.value.push(item);
  }

  function updateBlocksOrder(newBlocks: ItemBlock[]) {
    // Update the blocks array with the new order
    blocks.value = newBlocks;

    // Update sequence numbers to match the new order
    blocks.value.forEach((block, index) => {
      block.sequence = index + 1;
    });

    // Also update the current assessment if it exists (for evaluate type)
    if (currentAssessment.value && currentAssessment.value.itemBlocks) {
      // Update the assessment store with the new order
      currentAssessment.value.itemBlocks = [...blocks.value];
    }
  }

  function setSection(value: number, index: number) {
    blocks.value[index] = {
      ...blocks.value[index],
      section: value,
    } as ItemBlock;
  }

  function updateBlock(item: ItemBlock, index: number) {
    blocks.value[index] = item;
  }

  async function deleteBlock(index: number) {
    const deletedBlock = blocks.value[index];
    blocks.value.splice(index, 1);

    if (deletedBlock && deletedBlock.type === 'HEADER') {
      const deletedSectionNumber = deletedBlock.section;

      const blocksToDelete = blocks.value.filter((block) => block.section === deletedSectionNumber);
      blocks.value = blocks.value.filter((block) => block.section !== deletedSectionNumber);

      const headerBlocks = blocks.value
        .filter((block) => block.type === 'HEADER')
        .sort((a, b) => a.section - b.section);

      const sectionMap = new Map<number, number>();
      headerBlocks.forEach((header, idx) => {
        const oldSection = header.section;
        const newSection = idx + 1;
        sectionMap.set(oldSection, newSection);
        header.section = newSection;
      });

      const blocksNeedingUpdate: ItemBlock[] = [];
      blocks.value.forEach((block) => {
        if (sectionMap.has(block.section)) {
          const oldSection = block.section;
          const newSection = sectionMap.get(block.section)!;
          if (oldSection !== newSection) {
            block.section = newSection;
            blocksNeedingUpdate.push(block);
          }
        }
      });

      if (currentAssessment.value && currentAssessment.value.itemBlocks) {
        currentAssessment.value.itemBlocks = currentAssessment.value.itemBlocks.filter((block) => {
          if (block.section === deletedSectionNumber) return false;
          if (sectionMap.has(block.section)) {
            block.section = sectionMap.get(block.section)!;
          }
          return true;
        });
      }

      const assessmentId = currentAssessment.value?.id;
      if (assessmentId) {
        const assessmentService = new AssessmentService('evaluate');

        try {
          if (blocksToDelete.length > 0) {
            await Promise.all(
              blocksToDelete.map(async (block) => {
                try {
                  await assessmentService.deleteBlock(block);
                } catch (error) {
                  console.error(`❌ Failed to delete block ${block.id}:`, error);
                }
              }),
            );
          }

          const allBlocksToUpdate = [...headerBlocks, ...blocksNeedingUpdate];
          if (allBlocksToUpdate.length > 0) {
            await Promise.all(
              allBlocksToUpdate.map(async (block) => {
                try {
                  await assessmentService.updateBlock({ ...block, assessmentId });
                } catch (error) {
                  console.error(`❌ Failed to update block ${block.id}:`, error);
                }
              }),
            );
          }
        } catch (error) {
          console.error('❌ Error during backend operations:', error);
        }
      }

      await forceRefreshBlocks();

      if (currentAssessment.value) {
        currentAssessment.value.itemBlocks = [...blocks.value];
        await nextTick();
      }

      for (let i = 0; i < 5; i++) {
        forceUpdateTrigger.value++;
        await nextTick();
      }

      setTimeout(() => {
        forceUpdateTrigger.value++;
        void nextTick().then(() => {
          setTimeout(() => {
            forceUpdateTrigger.value++;
            void nextTick().then(() => {
              setTimeout(() => {
                forceUpdateTrigger.value++;
              }, 100);
            });
          }, 100);
        });
      }, 200);
    }
  }

  function duplicateBlock(source: ItemBlock, index: number) {
    const newBlock: ItemBlock = {
      ...source,
    };
    blocks.value.splice(index + 1, 0, newBlock);
    return newBlock;
  }

  // FAB protection utility - optimized for performance
  const createFabProtection = (blockId: number, duration = 200) => {
    blockCreationInProgress.value = true;
    targetBlockId.value = blockId;
    fabPositionLock.value = true;
    selectedBlockId.value = `block-${blockId}`;

    // Clear any existing protection timeout
    timeoutManager.clearLockTimeout();

    // Set new protection timeout
    timeoutManager.lockReleaseTimeout = setTimeout(() => {
      fabPositionLock.value = false;
      blockCreationInProgress.value = false;
      targetBlockId.value = null;
      // Ensure FAB stays on the updated block
      selectedBlockId.value = `block-${blockId}`;
    }, duration);
  };

  // Scroll and focus management
  const scrollToTarget = () => {
    if (!selectedBlockId.value) return;
    const id = Number(selectedBlockId.value.split('-')[1]);
    const el = getBlockRef(id);
    if (el && 'scrollIntoView' in el) {
      (el as HTMLElement).scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  };

  const setFabAndScroll = async (id: number) => {
    // Use immediate positioning to prevent conflicts during block creation
    setFabPosition(id, true);

    // Wait for DOM updates
    await nextTick();
    await nextTick();

    // Scroll to the target block
    scrollToTarget();
  };

  // Block creation methods
  const handleAddBlockAfter = async (
    index: number,
    type: 'quiz' | 'evaluate',
    assessmentId?: number | null,
  ) => {
    // Prevent multiple simultaneous block creation
    if (isCreatingBlock.value) {
      return;
    }

    try {
      isCreatingBlock.value = true;
      blockCreationInProgress.value = true;

      // Start save operation indicator
      const globalStore = useGlobalStore();
      globalStore.startSaveOperation('Creating new question...');

      // Enhanced ID validation using store helpers
      const finalAssessmentId = assessmentId || getAssessmentId();

      if (!finalAssessmentId) {
        globalStore.completeSaveOperation(false, 'Assessment ID not found');
        return;
      }

      // Validate that we have proper ID structure
      const validation = validateIds();
      if (!validation.valid) {
        console.warn('⚠️ ID validation failed before adding block:', validation.missing);
      }

      const globalIsRequired = currentAssessment.value?.globalIsRequired ?? false;
      const currentSection = getCurrentSection(blocks.value, index);

      const newBlockData = {
        assessmentId: finalAssessmentId,
        sequence: index + 2,
        section: currentSection,
        type: 'RADIO' as const,
        isRequired: globalIsRequired,
      };

      // Call backend API to create the block
      const assessmentService = new AssessmentService(type);
      const addedBlock = await assessmentService.createBlock(newBlockData);

      if (addedBlock) {
        // AGGRESSIVE APPROACH: Set FAB position IMMEDIATELY and lock it completely
        const newBlockId = addedBlock.id;

        // Step 1: Completely disable all FAB events during creation
        blockCreationInProgress.value = true;
        targetBlockId.value = newBlockId;
        fabPositionLock.value = true;

        // Step 2: Force FAB position BEFORE any DOM changes
        selectedBlockId.value = `block-${newBlockId}`;

        // Step 3: Add to local store
        addBlock(addedBlock, index);

        // Update the current assessment if this is an evaluate type
        if (type === 'evaluate' && currentAssessment.value) {
          const currentBlocks = currentAssessment.value.itemBlocks || [];
          // Insert at correct position (index + 1)
          const newAssessmentBlocks = [
            ...currentBlocks.slice(0, index + 1),
            addedBlock,
            ...currentBlocks.slice(index + 1),
          ];
          // Update sequences for assessment blocks
          newAssessmentBlocks.forEach((block, idx) => {
            block.sequence = idx + 1;
          });
          currentAssessment.value.itemBlocks = newAssessmentBlocks;
        }

        // Step 5: Complete save operation
        globalStore.completeSaveOperation(true, 'Question created successfully');

        // Step 6: Wait for DOM to settle, then scroll
        await nextTick();
        await nextTick();
        scrollToTarget();

        // Step 7: Clean up after extended period
        setTimeout(() => {
          fabPositionLock.value = false;
          blockCreationInProgress.value = false;
          targetBlockId.value = null;
          // Final enforcement
          selectedBlockId.value = `block-${newBlockId}`;
        }, 1000); // Extended cleanup period
      } else {
        globalStore.completeSaveOperation(false, 'Failed to create question');
      }
    } catch (error) {
      console.error('❌ Error creating new block:', error);
      const globalStore = useGlobalStore();
      globalStore.completeSaveOperation(false, 'Error creating question');
    } finally {
      isCreatingBlock.value = false;
      // Clean up state in case of errors
      if (blockCreationInProgress.value) {
        blockCreationInProgress.value = false;
        targetBlockId.value = null;
        fabPositionLock.value = false;
      }
    }
  };

  const handleAddHeaderAfter = async (
    index: number,
    type: 'quiz' | 'evaluate',
    assessmentId?: number | null,
  ) => {
    // Prevent multiple simultaneous block creation
    if (isCreatingBlock.value) {
      return;
    }

    try {
      isCreatingBlock.value = true;
      blockCreationInProgress.value = true;

      // Start save operation indicator
      const globalStore = useGlobalStore();
      globalStore.startSaveOperation('Creating new header...');

      const finalAssessmentId = assessmentId || getAssessmentId();
      if (!finalAssessmentId) {
        globalStore.completeSaveOperation(false, 'Assessment ID not found');
        console.error('❌ Assessment ID is required for creating header block');
        return;
      }

      const currentSection = getCurrentSection(blocks.value, index);

      const newHeaderData = {
        assessmentId: finalAssessmentId,
        sequence: index + 2,
        section: currentSection,
        type: 'HEADER' as const,
        isRequired: false,
      };

      // Call backend API to create the header block
      const assessmentService = new AssessmentService(type);
      const addedBlock = await assessmentService.createBlock(newHeaderData);

      if (addedBlock) {
        // CRITICAL: Set target block ID to allow only this block to receive focus
        targetBlockId.value = addedBlock.id;

        // CRITICAL: Lock FAB position BEFORE any DOM updates to prevent jumping
        fabPositionLock.value = true;
        selectedBlockId.value = `block-${addedBlock.id}`;

        // Add to local store with backend response data
        addBlock(addedBlock, index);

        // Update the current assessment if this is an evaluate type
        if (type === 'evaluate' && currentAssessment.value) {
          const currentBlocks = currentAssessment.value.itemBlocks || [];
          // Insert at correct position (index + 1)
          const newAssessmentBlocks = [
            ...currentBlocks.slice(0, index + 1),
            addedBlock,
            ...currentBlocks.slice(index + 1),
          ];
          // Update sequences for assessment blocks
          newAssessmentBlocks.forEach((block, idx) => {
            block.sequence = idx + 1;
          });
          currentAssessment.value.itemBlocks = newAssessmentBlocks;
        }

        // Complete save operation successfully
        globalStore.completeSaveOperation(true, 'Header created successfully');

        // Set FAB position and scroll (FAB is already locked to correct position)
        await setFabAndScroll(addedBlock.id);

        // Release all locks after DOM is stable
        setTimeout(() => {
          fabPositionLock.value = false;
          blockCreationInProgress.value = false;
          targetBlockId.value = null;
          // Final confirmation of FAB position
          selectedBlockId.value = `block-${addedBlock.id}`;
        }, 800); // Longer delay to ensure complete DOM stability
      } else {
        globalStore.completeSaveOperation(false, 'Failed to create header');
        console.error('❌ Failed to create header block - no response from backend');
      }
    } catch (error) {
      const globalStore = useGlobalStore();
      globalStore.completeSaveOperation(false, 'Error creating header');
      console.error('❌ Error creating header block:', error);
      console.error('Error context:', {
        assessmentId: assessmentId || getAssessmentId(),
        currentAssessment: !!currentAssessment.value,
        blockIndex: index,
      });
    } finally {
      isCreatingBlock.value = false;
      // Clean up state in case of errors
      if (blockCreationInProgress.value) {
        blockCreationInProgress.value = false;
        targetBlockId.value = null;
        fabPositionLock.value = false;
      }
    }
  };

  // Header block duplication method using ATOMIC backend operation
  const handleDuplicateHeaderBlock = async (
    blockId: number,
    type: 'quiz' | 'evaluate',
    assessmentId?: number | null,
  ) => {
    // Prevent multiple simultaneous block creation
    if (isCreatingBlock.value) {
      console.warn('⚠️ Block creation already in progress, skipping header duplication');
      return;
    }

    // Find the source header block
    const sourceBlockIndex = blocks.value.findIndex((block) => block.id === blockId);
    if (sourceBlockIndex === -1) {
      console.error('❌ Source header block not found for duplication:', blockId);
      return;
    }

    const sourceBlock = blocks.value[sourceBlockIndex];
    if (!sourceBlock || sourceBlock.type !== 'HEADER') {
      console.error('❌ Source block is not a header block:', sourceBlock);
      return;
    }

    try {
      isCreatingBlock.value = true;
      blockCreationInProgress.value = true;

      // Start save operation indicator
      const globalStore = useGlobalStore();
      globalStore.startSaveOperation('Duplicating header (atomic operation)...');

      const finalAssessmentId = assessmentId || getAssessmentId();
      if (!finalAssessmentId) {
        globalStore.completeSaveOperation(false, 'Assessment ID not found');
        console.error('❌ Assessment ID is required for header block duplication');
        return;
      }

      const currentSection = getCurrentSection(blocks.value, sourceBlockIndex);

      // ATOMIC DUPLICATION: Single backend call creates complete duplicate
      const assessmentService = new AssessmentService(type);
      const duplicatedBlock = await assessmentService.duplicateBlock(blockId, {
        assessmentId: finalAssessmentId,
        sequence: sourceBlock.sequence + 1,
        section: currentSection,
      });

      if (!duplicatedBlock) {
        globalStore.completeSaveOperation(false, 'Failed to duplicate header');
        console.error('❌ Failed to duplicate header block - no response from backend');
        return;
      }

      // Insert the duplicated block immediately after the source block
      const insertIndex = sourceBlockIndex + 1;
      addBlock(duplicatedBlock, insertIndex);

      // Update sequences for all blocks after insertion
      blocks.value.forEach((block, idx) => {
        if (block.sequence !== idx + 1) {
          block.sequence = idx + 1;
        }
      });

      // Update the current assessment if this is an evaluate type
      if (type === 'evaluate' && currentAssessment.value) {
        const currentBlocks = currentAssessment.value.itemBlocks || [];
        // Insert at correct position (insertIndex)
        const newAssessmentBlocks = [
          ...currentBlocks.slice(0, insertIndex),
          duplicatedBlock,
          ...currentBlocks.slice(insertIndex),
        ];
        // Update sequences for assessment blocks
        newAssessmentBlocks.forEach((block, idx) => {
          block.sequence = idx + 1;
        });
        currentAssessment.value.itemBlocks = newAssessmentBlocks;
      }

      // CRITICAL: Set target block ID to allow only this block to receive focus
      targetBlockId.value = duplicatedBlock.id;
      // CRITICAL: Lock FAB position BEFORE any DOM updates to prevent jumping
      fabPositionLock.value = true;

      // Set FAB position and scroll to the new block after it is added
      await setFabAndScroll(duplicatedBlock.id);

      // Force FAB position on the duplicated block
      selectedBlockId.value = `block-${duplicatedBlock.id}`;

      // Complete save operation successfully
      const hasContent =
        duplicatedBlock.headerBody &&
        (duplicatedBlock.headerBody.title || duplicatedBlock.headerBody.description);
      globalStore.completeSaveOperation(
        true,
        hasContent
          ? 'Header duplicated successfully with content (atomic)'
          : 'Header duplicated successfully (atomic)',
      );

      // Release locks after DOM is stable (reduced timeout since no race condition)
      setTimeout(() => {
        fabPositionLock.value = false;
        blockCreationInProgress.value = false;
        targetBlockId.value = null;
        // Final confirmation of FAB position on the duplicated block
        selectedBlockId.value = `block-${duplicatedBlock.id}`;
      }, 300);
    } catch (error) {
      console.error('❌ Error during header block duplication:', error);
      const globalStore = useGlobalStore();
      globalStore.completeSaveOperation(false, 'Error duplicating header');
    } finally {
      isCreatingBlock.value = false;
      // Clean up state in case of errors
      if (blockCreationInProgress.value) {
        blockCreationInProgress.value = false;
        targetBlockId.value = null;
        fabPositionLock.value = false;
      }
    }
  };

  // Add Section (create a new section at the end)
  const handleAddSection = async (type: 'quiz' | 'evaluate', assessmentId?: number | null) => {
    if (isCreatingBlock.value) {
      return;
    }
    try {
      isCreatingBlock.value = true;
      blockCreationInProgress.value = true;
      const globalStore = useGlobalStore();
      globalStore.startSaveOperation('Creating new section...');
      const finalAssessmentId = assessmentId || getAssessmentId();
      if (!finalAssessmentId) {
        globalStore.completeSaveOperation(false, 'Assessment ID not found');
        return;
      }
      // Find the highest section number
      const maxSection = blocks.value.reduce((max, block) => Math.max(max, block.section || 1), 1);
      const newSectionNumber = maxSection + 1;
      // Add a HEADER block as the start of the new section
      const newHeaderData = {
        assessmentId: finalAssessmentId,
        sequence: blocks.value.length + 1,
        section: newSectionNumber,
        type: 'HEADER' as const,
        isRequired: false,
      };
      const assessmentService = new AssessmentService(type);
      const addedBlock = await assessmentService.createBlock(newHeaderData);
      if (addedBlock) {
        // Add to local store
        appendBlock(addedBlock);
        // Update the current assessment if this is an evaluate type
        if (type === 'evaluate' && currentAssessment.value) {
          const currentBlocks = currentAssessment.value.itemBlocks || [];
          const newAssessmentBlocks = [...currentBlocks, addedBlock];
          newAssessmentBlocks.forEach((block, idx) => {
            block.sequence = idx + 1;
          });
          currentAssessment.value.itemBlocks = newAssessmentBlocks;
        }
        // Set FAB position and scroll
        await setFabAndScroll(addedBlock.id);
        selectedBlockId.value = `block-${addedBlock.id}`;
        globalStore.completeSaveOperation(true, 'Section created successfully');
        setTimeout(() => {
          fabPositionLock.value = false;
          blockCreationInProgress.value = false;
          targetBlockId.value = null;
          selectedBlockId.value = `block-${addedBlock.id}`;
        }, 800);
      } else {
        globalStore.completeSaveOperation(false, 'Failed to create section');
      }
    } catch (error) {
      const globalStore = useGlobalStore();
      globalStore.completeSaveOperation(false, 'Error creating section');
      console.error('❌ Error creating section:', error);
    } finally {
      isCreatingBlock.value = false;
      if (blockCreationInProgress.value) {
        blockCreationInProgress.value = false;
        targetBlockId.value = null;
        fabPositionLock.value = false;
      }
    }
  };

  // Reactive flag to force UI updates
  const forceUpdateTrigger = ref(0);

  async function forceRefreshBlocks() {
    forceUpdateTrigger.value++;
    await nextTick();
    forceUpdateTrigger.value++;
    await nextTick();
  }

  const syncBlocksWithAssessment = async () => {
    if (currentAssessment.value && currentAssessment.value.itemBlocks) {
      // Sync blocks with currentAssessment
      blocks.value = [...currentAssessment.value.itemBlocks];
      await forceRefreshBlocks();
    }
  };

  // Section helpers
  function isSectionBlock(index: number) {
    return blocks.value[index]?.type === 'HEADER';
  }

  const totalSections = computed(() => {
    return blocks.value.filter((block) => block.type === 'HEADER').length;
  });

  function getSectionNumber(index: number) {
    let sectionCounter = 0;
    for (let i = 0; i <= index; i++) {
      if (blocks.value[i]?.type === 'HEADER') {
        sectionCounter++;
      }
    }
    return sectionCounter;
  }

  // Block refs for scrolling/focus
  function setBlockRef(id: number, el: DOMRefElement) {
    blockRefs[id] = el;
  }
  function getBlockRef(id: number) {
    return blockRefs[id];
  }

  // Initialization helpers
  function resetBlocks(initialBlocks: ItemBlock[]) {
    blocks.value = initialBlocks;
  }

  function initializeBlocks(initialBlocks: ItemBlock[]) {
    blocks.value = [...initialBlocks];
  }

  function isAnswerItemBlockType(type: string): boolean {
    return allowedTypes.includes(type);
  }

  // Assessment management functions (migrated from form.ts)
  const fetchAssessmentById = async (id: number) => {
    loading.value = true;
    error.value = null;
    try {
      const res = await new AssessmentService('evaluate').fetchOne(id);
      if (res) {
        currentAssessment.value = res;
        // Sync blocks with assessment
        if (res.itemBlocks) {
          blocks.value = res.itemBlocks;
        }
      }
    } catch (err: unknown) {
      error.value = err instanceof Error ? err.message : 'ไม่สามารถโหลดแบบทดสอบได้';
    } finally {
      loading.value = false;
    }
  };

  const addAssessment = async (assessmentData: Partial<Assessment>): Promise<Assessment> => {
    const res = await new AssessmentService('evaluate').createOne(assessmentData);
    assessments.value.push(res);
    currentAssessment.value = res;
    return res;
  };

  const updateAssessment = async (id: number, assessmentData: Assessment): Promise<Assessment> => {
    const res = await new AssessmentService('evaluate').updateOne(id, assessmentData);
    const index = assessments.value.findIndex((q) => q.id === id);
    if (index !== -1) assessments.value[index] = res;
    if (currentAssessment.value?.id === id) currentAssessment.value = res;
    return res;
  };

  const removeAssessment = async (id: number): Promise<void> => {
    await new AssessmentService('evaluate').deleteOne(id);
    assessments.value = assessments.value.filter((q) => q.id !== id);
    if (currentAssessment.value?.id === id) {
      currentAssessment.value = null;
    }
  };

  // ID Tracking Helpers (migrated from form.ts)
  const getAssessmentId = (): number | null => {
    return currentAssessment.value?.id || null;
  };

  const getItemBlockById = (id: number) => {
    return currentAssessment.value?.itemBlocks?.find((block) => block.id === id) || null;
  };

  const getHeaderBlockId = (): number | null => {
    const headerBlock = currentAssessment.value?.itemBlocks?.find(
      (block) => block.type === 'HEADER',
    );
    return headerBlock?.id || null;
  };

  const getRadioBlockId = (): number | null => {
    const radioBlock = currentAssessment.value?.itemBlocks?.find((block) => block.type === 'RADIO');
    return radioBlock?.id || null;
  };

  const getAllItemBlockIds = (): number[] => {
    return currentAssessment.value?.itemBlocks?.map((block) => block.id) || [];
  };

  // Validation Helpers (migrated from form.ts)
  const validateIds = (): { valid: boolean; missing: string[] } => {
    const missing: string[] = [];

    if (!currentAssessment.value?.id) {
      missing.push('assessmentId');
    }

    if (!currentAssessment.value?.itemBlocks || currentAssessment.value.itemBlocks.length === 0) {
      missing.push('itemBlocks');
    } else {
      currentAssessment.value.itemBlocks.forEach((block, index) => {
        if (!block.id) {
          missing.push(`itemBlock[${index}].id`);
        }
        if (!block.assessmentId) {
          missing.push(`itemBlock[${index}].assessmentId`);
        }
      });
    }

    return {
      valid: missing.length === 0,
      missing,
    };
  };

  // Enhanced Validation for Block Deletion (migrated from form.ts)
  const validateBlockDeletion = (blockId: number): { canDelete: boolean; issues: string[] } => {
    const issues: string[] = [];

    if (!currentAssessment.value) {
      issues.push('No current assessment loaded');
      return { canDelete: false, issues };
    }

    if (!blockId) {
      issues.push('Invalid block ID provided');
      return { canDelete: false, issues };
    }

    const targetBlock = currentAssessment.value.itemBlocks?.find((block) => block.id === blockId);
    if (!targetBlock) {
      issues.push(`Block with ID ${blockId} not found in current assessment`);
      return { canDelete: false, issues };
    }

    // Additional validation for header blocks
    if (targetBlock.type === 'HEADER' && !targetBlock.headerBody) {
      issues.push('Header block missing headerBody data');
    }

    // Check for orphaned references
    if (targetBlock.assessmentId !== currentAssessment.value.id) {
      issues.push(
        `Block assessmentId (${targetBlock.assessmentId}) does not match current assessment ID (${currentAssessment.value.id})`,
      );
    }

    return {
      canDelete: issues.length === 0,
      issues,
    };
  };

  // Post-Deletion Validation (migrated from form.ts)
  const validatePostDeletion = (deletedBlockId: number): { success: boolean; issues: string[] } => {
    const issues: string[] = [];

    if (!currentAssessment.value) {
      issues.push('No current assessment loaded');
      return { success: false, issues };
    }

    // Check if the block still exists in the assessment
    const blockStillExists = currentAssessment.value.itemBlocks?.some(
      (block) => block.id === deletedBlockId,
    );
    if (blockStillExists) {
      issues.push(`Block with ID ${deletedBlockId} still exists in assessment after deletion`);
    }

    // Check for any orphaned references
    const orphanedQuestions = currentAssessment.value.itemBlocks?.some((block) =>
      block.questions?.some((question) => question.itemBlockId === deletedBlockId),
    );
    if (orphanedQuestions) {
      issues.push(`Found orphaned questions referencing deleted block ID ${deletedBlockId}`);
    }

    const orphanedOptions = currentAssessment.value.itemBlocks?.some((block) =>
      block.options?.some((option) => option.itemBlockId === deletedBlockId),
    );
    if (orphanedOptions) {
      issues.push(`Found orphaned options referencing deleted block ID ${deletedBlockId}`);
    }

    return {
      success: issues.length === 0,
      issues,
    };
  };

  // Assessment data conversion
  function getAssessmentData() {
    return {
      blocks: blocks.value,
      totalBlocks: blocks.value.length,
      totalSections: totalSections.value,
    };
  }

  // Expose API
  return {
    // Original block creator functionality
    isAnswerItemBlockType,
    blocks,
    addBlock,
    appendBlock,
    updateBlocksOrder,
    setSection,
    updateBlock,
    deleteBlock,
    duplicateBlock,
    isSectionBlock,
    totalSections,
    getSectionNumber,
    setBlockRef,
    getBlockRef,
    resetBlocks,
    initializeBlocks,
    generateQuestionId,
    generateOptionId,
    selectedBlock,
    selectedBlockId,
    getAssessmentData,

    // Assessment management (migrated from form.ts)
    assessments,
    currentAssessment,
    loading,
    error,
    meta,
    page,
    limit,
    search,
    fetchAssessmentById,
    addAssessment,
    updateAssessment,
    removeAssessment,

    // ID tracking helpers (migrated from form.ts)
    getAssessmentId,
    getItemBlockById,
    getHeaderBlockId,
    getRadioBlockId,
    getAllItemBlockIds,
    validateIds,

    // Enhanced validation helpers (migrated from form.ts)
    validateBlockDeletion,
    validatePostDeletion,

    // Global state (migrated from formItemCreateStore.ts)
    duplicateDialog,

    // Block creation and UI state
    isCreatingBlock,
    isDragging,
    fabState,
    fabPositionLock,
    pendingFabPosition,
    blockCreationInProgress,
    targetBlockId,

    // FAB and scroll management
    timeoutManager,
    setFabPosition,
    createFabProtection,
    scrollToTarget,
    setFabAndScroll,

    // Block operations
    handleAddBlockAfter,
    handleAddHeaderAfter,
    handleDuplicateHeaderBlock,
    handleAddSection,

    // UI state management
    forceRefreshBlocks,
    forceUpdateTrigger,
    syncBlocksWithAssessment,
  };
});
