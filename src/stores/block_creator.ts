import { defineStore } from 'pinia';
import type { ItemBlock, Assessment } from 'src/types/models';
import type { DataResponse } from 'src/types/data';
import { ref, computed, nextTick } from 'vue';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { getCurrentSection } from 'src/utils/block_helper';
import { useGlobalStore } from './global';

type DOMRefElement = Element | null;

// ===== CONSTANTS =====
const ALLOWED_BLOCK_TYPES: string[] = ['RADIO', 'CHECKBOX', 'TEXTFIELD', 'GRID'];
const FAB_POSITION_DEBOUNCE_DELAY = 50;
const FAB_LOCK_RELEASE_DELAY = 200;
const FAB_PROTECTION_DURATION = 200;
const UI_REFRESH_ITERATIONS = 5;
const DELAYED_REFRESH_DELAYS = [200, 100, 100];

export const useBlockCreatorStore = defineStore('blockCreator', () => {
  // ===== BLOCK MANAGEMENT STATE =====
  const blockList = ref<ItemBlock[]>([]);
  const blockDOMReferences: Record<number, DOMRefElement> = {};
  const currentSelectedBlock = ref<ItemBlock | null>(null);
  const currentSelectedBlockId = ref<string | undefined>();

  // ===== ASSESSMENT MANAGEMENT STATE =====
  const assessmentMetadata = ref<DataResponse<Assessment> | null>(null);
  const assessmentCollection = ref<Assessment[]>([]);
  const activeAssessment = ref<Assessment | null>(null);
  const isLoadingAssessment = ref(false);
  const assessmentError = ref<string | null>(null);
  const paginationPage = ref(1);
  const paginationLimit = ref(5);
  const searchQuery = ref('');

  // ===== UI INTERACTION STATE =====
  const showDuplicateDialog = ref(false);
  const isBlockCreationInProgress = ref(false);
  const isDragOperationActive = ref(false);
  const uiRefreshTrigger = ref(0);

  // ===== FAB POSITIONING STATE =====
  const fabPositioningState = ref({
    isPositionLocked: false,
    pendingTargetPosition: null as number | null,
    isCreationInProgress: false,
    targetBlockId: null as number | null,
  });

  // ===== ID GENERATION STATE =====
  const questionIdCounter = ref(0);
  const optionIdCounter = ref(0);

  // ===== ID GENERATION UTILITIES =====
  function generateNextQuestionId(): number {
    questionIdCounter.value++;
    return questionIdCounter.value;
  }

  function generateNextOptionId(): number {
    optionIdCounter.value++;
    return optionIdCounter.value;
  }

  // ===== FAB STATE COMPUTED PROPERTIES =====
  const isFabPositionLocked = computed({
    get: () => fabPositioningState.value.isPositionLocked,
    set: (value: boolean) => {
      fabPositioningState.value.isPositionLocked = value;
    },
  });

  const pendingFabTargetPosition = computed({
    get: () => fabPositioningState.value.pendingTargetPosition,
    set: (value: number | null) => {
      fabPositioningState.value.pendingTargetPosition = value;
    },
  });

  const isFabCreationInProgress = computed({
    get: () => fabPositioningState.value.isCreationInProgress,
    set: (value: boolean) => {
      fabPositioningState.value.isCreationInProgress = value;
    },
  });

  const fabTargetBlockId = computed({
    get: () => fabPositioningState.value.targetBlockId,
    set: (value: number | null) => {
      fabPositioningState.value.targetBlockId = value;
    },
  });

  // ===== TIMEOUT MANAGEMENT UTILITIES =====
  const fabTimeoutManager = {
    fabPositionTimeout: null as NodeJS.Timeout | null,
    lockReleaseTimeout: null as NodeJS.Timeout | null,

    clearFabPositionTimeout() {
      if (this.fabPositionTimeout) {
        clearTimeout(this.fabPositionTimeout);
        this.fabPositionTimeout = null;
      }
    },

    clearLockReleaseTimeout() {
      if (this.lockReleaseTimeout) {
        clearTimeout(this.lockReleaseTimeout);
        this.lockReleaseTimeout = null;
      }
    },

    clearAllTimeouts() {
      this.clearFabPositionTimeout();
      this.clearLockReleaseTimeout();
    },
  };

  // ===== BLOCK DOM REFERENCE MANAGEMENT =====
  function setBlockDOMReference(blockId: number, element: DOMRefElement) {
    blockDOMReferences[blockId] = element;
  }

  function getBlockDOMReference(blockId: number) {
    return blockDOMReferences[blockId];
  }

  // ===== BLOCK OPERATIONS =====
  function insertBlockAfterIndex(newBlock: ItemBlock, targetIndex: number) {
    blockList.value.splice(targetIndex + 1, 0, newBlock);
  }

  function addBlockToEnd(newBlock: ItemBlock) {
    blockList.value.push(newBlock);
  }

  function reorderBlocksAndUpdateSequences(reorderedBlocks: ItemBlock[]) {
    blockList.value = reorderedBlocks;
    updateAllBlockSequences();
    synchronizeAssessmentWithBlocks();
  }

  function updateBlockSection(sectionNumber: number, blockIndex: number) {
    blockList.value[blockIndex] = {
      ...blockList.value[blockIndex],
      section: sectionNumber,
    } as ItemBlock;
  }

  function replaceBlockAtIndex(updatedBlock: ItemBlock, targetIndex: number) {
    blockList.value[targetIndex] = updatedBlock;
  }

  function createDuplicateBlock(sourceBlock: ItemBlock, insertionIndex: number) {
    const duplicatedBlock: ItemBlock = { ...sourceBlock };
    blockList.value.splice(insertionIndex + 1, 0, duplicatedBlock);
    return duplicatedBlock;
  }

  // ===== SEQUENCE MANAGEMENT UTILITIES =====
  function updateAllBlockSequences() {
    blockList.value.forEach((block, index) => {
      block.sequence = index + 1;
    });
  }

  function synchronizeAssessmentWithBlocks() {
    if (activeAssessment.value && activeAssessment.value.itemBlocks) {
      activeAssessment.value.itemBlocks = [...blockList.value];
    }
  }

  // ===== UI REFRESH UTILITIES =====
  async function executeBasicUIRefresh() {
    uiRefreshTrigger.value++;
    await nextTick();
    uiRefreshTrigger.value++;
    await nextTick();
  }

  async function executeComplexUIRefresh() {
    await executeBasicUIRefresh();

    if (activeAssessment.value) {
      activeAssessment.value.itemBlocks = [...blockList.value];
      await nextTick();
    }

    // Multiple refresh cycles for complex DOM changes
    for (let i = 0; i < UI_REFRESH_ITERATIONS; i++) {
      uiRefreshTrigger.value++;
      await nextTick();
    }

    // Delayed refresh cycles with cleaner timeout management
    for (const delay of DELAYED_REFRESH_DELAYS) {
      await executeDelayedRefresh(delay);
      await nextTick();
    }
  }

  async function executeDelayedRefresh(delay: number): Promise<void> {
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        uiRefreshTrigger.value++;
        resolve();
      }, delay);
    });
  }

  async function synchronizeBlocksWithAssessment() {
    if (activeAssessment.value && activeAssessment.value.itemBlocks) {
      blockList.value = [...activeAssessment.value.itemBlocks];
      await executeBasicUIRefresh();
    }
  }

  // ===== FAB POSITIONING UTILITIES =====
  function updateFabPosition(blockId: number, shouldPositionImmediately = false) {
    if (isBlockCreationInProgress.value && !shouldPositionImmediately) {
      pendingFabTargetPosition.value = blockId;
      return;
    }

    fabTimeoutManager.clearFabPositionTimeout();

    if (shouldPositionImmediately) {
      positionFabImmediately(blockId);
    } else {
      positionFabWithDebounce(blockId);
    }
  }

  function positionFabImmediately(blockId: number) {
    isFabPositionLocked.value = true;
    currentSelectedBlockId.value = `block-${blockId}`;
    fabTimeoutManager.clearLockReleaseTimeout();

    fabTimeoutManager.lockReleaseTimeout = setTimeout(() => {
      isFabPositionLocked.value = false;
      const pendingPosition = pendingFabTargetPosition.value;
      if (pendingPosition && pendingPosition !== blockId) {
        updateFabPosition(pendingPosition, false);
        pendingFabTargetPosition.value = null;
      }
    }, FAB_LOCK_RELEASE_DELAY);
  }

  function positionFabWithDebounce(blockId: number) {
    fabTimeoutManager.fabPositionTimeout = setTimeout(() => {
      if (!isFabPositionLocked.value) {
        currentSelectedBlockId.value = `block-${blockId}`;
      } else {
        pendingFabTargetPosition.value = blockId;
      }
    }, FAB_POSITION_DEBOUNCE_DELAY);
  }

  function createFabProtectionForBlock(blockId: number, duration = FAB_PROTECTION_DURATION) {
    isFabCreationInProgress.value = true;
    fabTargetBlockId.value = blockId;
    isFabPositionLocked.value = true;
    currentSelectedBlockId.value = `block-${blockId}`;

    fabTimeoutManager.clearLockReleaseTimeout();

    fabTimeoutManager.lockReleaseTimeout = setTimeout(() => {
      isFabPositionLocked.value = false;
      isFabCreationInProgress.value = false;
      fabTargetBlockId.value = null;
      currentSelectedBlockId.value = `block-${blockId}`;
    }, duration);
  }

  // ===== SCROLL UTILITIES =====
  function scrollToSelectedBlock() {
    if (!currentSelectedBlockId.value) return;
    const blockId = Number(currentSelectedBlockId.value.split('-')[1]);
    const blockElement = getBlockDOMReference(blockId);
    if (blockElement && 'scrollIntoView' in blockElement) {
      (blockElement as HTMLElement).scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    }
  }

  async function positionFabAndScrollToBlock(blockId: number) {
    updateFabPosition(blockId, true);
    await nextTick();
    await nextTick();
    scrollToSelectedBlock();
  }

  // ===== VALIDATION UTILITIES =====
  function validateAssessmentForOperation(operationName: string): boolean {
    if (!activeAssessment.value?.id) {
      console.error(`❌ Assessment ID is required for ${operationName}`);
      return false;
    }
    return true;
  }

  // ===== ASSESSMENT UTILITIES =====
  function getAssessmentId(): number | null {
    return activeAssessment.value?.id || null;
  }

  function getItemBlockById(id: number) {
    return activeAssessment.value?.itemBlocks?.find((block) => block.id === id) || null;
  }

  function getHeaderBlockId(): number | null {
    const headerBlock = activeAssessment.value?.itemBlocks?.find(
      (block) => block.type === 'HEADER',
    );
    return headerBlock?.id || null;
  }

  function getRadioBlockId(): number | null {
    const radioBlock = activeAssessment.value?.itemBlocks?.find((block) => block.type === 'RADIO');
    return radioBlock?.id || null;
  }

  function getAllItemBlockIds(): number[] {
    return activeAssessment.value?.itemBlocks?.map((block) => block.id) || [];
  }

  // ===== SECTION UTILITIES =====
  function isSectionBlock(index: number) {
    return blockList.value[index]?.type === 'HEADER';
  }

  const totalSections = computed(() => {
    return blockList.value.filter((block) => block.type === 'HEADER').length;
  });

  function getSectionNumber(index: number) {
    let sectionCounter = 0;
    for (let i = 0; i <= index; i++) {
      if (blockList.value[i]?.type === 'HEADER') {
        sectionCounter++;
      }
    }
    return sectionCounter;
  }

  function findMaxSectionNumber(): number {
    return blockList.value.reduce((max, block) => Math.max(max, block.section || 1), 1);
  }

  // ===== BLOCK TYPE UTILITIES =====
  function isAnswerItemBlockType(type: string): boolean {
    return ALLOWED_BLOCK_TYPES.includes(type);
  }

  function initializeBlocks(initialBlocks: ItemBlock[]) {
    blockList.value = [...initialBlocks];
  }

  function resetBlocks(initialBlocks: ItemBlock[]) {
    blockList.value = initialBlocks;
  }

  function getAssessmentData() {
    return {
      blocks: blockList.value,
      totalBlocks: blockList.value.length,
      totalSections: totalSections.value,
    };
  }

  // ===== BLOCK DELETION UTILITIES =====
  async function removeBlockFromList(targetIndex: number) {
    const blockToDelete = blockList.value[targetIndex];
    blockList.value.splice(targetIndex, 1);

    if (blockToDelete && blockToDelete.type === 'HEADER') {
      await handleHeaderBlockDeletion(blockToDelete);
    }
  }

  async function handleHeaderBlockDeletion(deletedHeaderBlock: ItemBlock) {
    const deletedSectionNumber = deletedHeaderBlock.section;

    const blocksInDeletedSection = blockList.value.filter(
      (block) => block.section === deletedSectionNumber,
    );

    blockList.value = blockList.value.filter((block) => block.section !== deletedSectionNumber);

    const { updatedBlocks, sectionMap } = updateSectionNumbersAfterDeletion(blockList.value);

    updateAssessmentAfterSectionDeletion(deletedSectionNumber, sectionMap);
    await performBlockDeletionBackendOperations(blocksInDeletedSection, updatedBlocks);
    await executeComplexUIRefresh();
  }

  function updateSectionNumbersAfterDeletion(remainingBlocks: ItemBlock[]) {
    const headerBlocks = remainingBlocks
      .filter((block) => block.type === 'HEADER')
      .sort((a, b) => a.section - b.section);

    const sectionMap = new Map<number, number>();
    headerBlocks.forEach((header, idx) => {
      const oldSection = header.section;
      const newSection = idx + 1;
      sectionMap.set(oldSection, newSection);
      header.section = newSection;
    });

    const blocksNeedingUpdate: ItemBlock[] = [];
    remainingBlocks.forEach((block) => {
      if (sectionMap.has(block.section)) {
        const oldSection = block.section;
        const newSection = sectionMap.get(block.section)!;
        if (oldSection !== newSection) {
          block.section = newSection;
          blocksNeedingUpdate.push(block);
        }
      }
    });

    return {
      updatedBlocks: [...headerBlocks, ...blocksNeedingUpdate],
      sectionMap,
    };
  }

  function updateAssessmentAfterSectionDeletion(
    deletedSectionNumber: number,
    sectionMap: Map<number, number>,
  ) {
    if (activeAssessment.value && activeAssessment.value.itemBlocks) {
      activeAssessment.value.itemBlocks = activeAssessment.value.itemBlocks.filter((block) => {
        if (block.section === deletedSectionNumber) return false;
        if (sectionMap.has(block.section)) {
          block.section = sectionMap.get(block.section)!;
        }
        return true;
      });
    }
  }

  async function performBlockDeletionBackendOperations(
    blocksToDelete: ItemBlock[],
    blocksToUpdate: ItemBlock[],
  ) {
    const assessmentId = activeAssessment.value?.id;
    if (!assessmentId) return;

    const assessmentService = new AssessmentService('evaluate');

    try {
      if (blocksToDelete.length > 0) {
        await Promise.all(
          blocksToDelete.map(async (block) => {
            try {
              await assessmentService.deleteBlock(block);
            } catch (error) {
              console.error(`❌ Failed to delete block ${block.id}:`, error);
            }
          }),
        );
      }

      if (blocksToUpdate.length > 0) {
        await Promise.all(
          blocksToUpdate.map(async (block) => {
            try {
              await assessmentService.updateBlock({ ...block, assessmentId });
            } catch (error) {
              console.error(`❌ Failed to update block ${block.id}:`, error);
            }
          }),
        );
      }
    } catch (error) {
      console.error('❌ Error during backend operations:', error);
    }
  }

  // ===== BLOCK CREATION UTILITIES =====
  async function createNewBlockAfterIndex(
    targetIndex: number,
    assessmentType: 'quiz' | 'evaluate',
    assessmentId?: number | null,
  ) {
    if (isBlockCreationInProgress.value) return;

    try {
      isBlockCreationInProgress.value = true;
      isFabCreationInProgress.value = true;

      const globalStore = useGlobalStore();
      globalStore.startSaveOperation('Creating new question...');

      const finalAssessmentId = assessmentId || getAssessmentId();
      if (!finalAssessmentId || !validateAssessmentForOperation('block creation')) {
        globalStore.completeSaveOperation(false, 'Assessment ID not found');
        return;
      }

      const globalIsRequired = activeAssessment.value?.globalIsRequired ?? false;
      const currentSection = getCurrentSection(blockList.value, targetIndex);

      const newBlockData = {
        assessmentId: finalAssessmentId,
        sequence: targetIndex + 2,
        section: currentSection,
        type: 'RADIO' as const,
        isRequired: globalIsRequired,
      };

      const assessmentService = new AssessmentService(assessmentType);
      const createdBlock = await assessmentService.createBlock(newBlockData);

      if (createdBlock) {
        await handleSuccessfulBlockCreation(createdBlock, targetIndex, assessmentType, globalStore);
      } else {
        globalStore.completeSaveOperation(false, 'Failed to create question');
      }
    } catch (error) {
      console.error('❌ Error creating new block:', error);
      const globalStore = useGlobalStore();
      globalStore.completeSaveOperation(false, 'Error creating question');
    } finally {
      isBlockCreationInProgress.value = false;
      cleanupBlockCreationState();
    }
  }

  async function handleSuccessfulBlockCreation(
    createdBlock: ItemBlock,
    targetIndex: number,
    assessmentType: 'quiz' | 'evaluate',
    globalStore: ReturnType<typeof useGlobalStore>,
  ) {
    const newBlockId = createdBlock.id;

    // Set FAB protection immediately
    fabTargetBlockId.value = newBlockId;
    isFabPositionLocked.value = true;
    currentSelectedBlockId.value = `block-${newBlockId}`;

    // Add to local store
    insertBlockAfterIndex(createdBlock, targetIndex);

    // Update assessment if needed
    if (assessmentType === 'evaluate' && activeAssessment.value) {
      updateAssessmentWithNewBlock(createdBlock, targetIndex);
    }

    globalStore.completeSaveOperation(true, 'Question created successfully');

    // Position FAB and scroll
    await positionFabAndScrollToBlock(newBlockId);

    // Extended cleanup period
    setTimeout(() => {
      cleanupBlockCreationState();
      currentSelectedBlockId.value = `block-${newBlockId}`;
    }, 1000);
  }

  function updateAssessmentWithNewBlock(newBlock: ItemBlock, insertIndex: number) {
    if (!activeAssessment.value) return;

    const currentBlocks = activeAssessment.value.itemBlocks || [];
    const newAssessmentBlocks = [
      ...currentBlocks.slice(0, insertIndex + 1),
      newBlock,
      ...currentBlocks.slice(insertIndex + 1),
    ];

    // Update sequences
    newAssessmentBlocks.forEach((block, idx) => {
      block.sequence = idx + 1;
    });

    activeAssessment.value.itemBlocks = newAssessmentBlocks;
  }

  function cleanupBlockCreationState() {
    if (isFabCreationInProgress.value) {
      isFabCreationInProgress.value = false;
      fabTargetBlockId.value = null;
      isFabPositionLocked.value = false;
    }
  }

  // ===== HEADER BLOCK CREATION =====
  async function createNewHeaderAfterIndex(
    targetIndex: number,
    assessmentType: 'quiz' | 'evaluate',
    assessmentId?: number | null,
  ) {
    if (isBlockCreationInProgress.value) return;

    try {
      isBlockCreationInProgress.value = true;
      isFabCreationInProgress.value = true;

      const globalStore = useGlobalStore();
      globalStore.startSaveOperation('Creating new header...');

      const finalAssessmentId = assessmentId || getAssessmentId();
      if (!finalAssessmentId || !validateAssessmentForOperation('header creation')) {
        globalStore.completeSaveOperation(false, 'Assessment ID not found');
        return;
      }

      const currentSection = getCurrentSection(blockList.value, targetIndex);
      const newHeaderData = {
        assessmentId: finalAssessmentId,
        sequence: targetIndex + 2,
        section: currentSection,
        type: 'HEADER' as const,
        isRequired: false,
      };

      const assessmentService = new AssessmentService(assessmentType);
      const createdHeader = await assessmentService.createBlock(newHeaderData);

      if (createdHeader) {
        await handleSuccessfulHeaderCreation(
          createdHeader,
          targetIndex,
          assessmentType,
          globalStore,
        );
      } else {
        globalStore.completeSaveOperation(false, 'Failed to create header');
      }
    } catch (error) {
      console.error('❌ Error creating header block:', error);
      const globalStore = useGlobalStore();
      globalStore.completeSaveOperation(false, 'Error creating header');
    } finally {
      isBlockCreationInProgress.value = false;
      cleanupBlockCreationState();
    }
  }

  async function handleSuccessfulHeaderCreation(
    createdHeader: ItemBlock,
    targetIndex: number,
    assessmentType: 'quiz' | 'evaluate',
    globalStore: ReturnType<typeof useGlobalStore>,
  ) {
    const headerId = createdHeader.id;

    // Set FAB protection
    fabTargetBlockId.value = headerId;
    isFabPositionLocked.value = true;
    currentSelectedBlockId.value = `block-${headerId}`;

    // Add to local store
    insertBlockAfterIndex(createdHeader, targetIndex);

    // Update assessment if needed
    if (assessmentType === 'evaluate' && activeAssessment.value) {
      updateAssessmentWithNewBlock(createdHeader, targetIndex);
    }

    globalStore.completeSaveOperation(true, 'Header created successfully');
    await positionFabAndScrollToBlock(headerId);

    setTimeout(() => {
      cleanupBlockCreationState();
      currentSelectedBlockId.value = `block-${headerId}`;
    }, 800);
  }

  // ===== BLOCK DUPLICATION =====
  async function duplicateHeaderBlockAtomically(
    sourceBlockId: number,
    assessmentType: 'quiz' | 'evaluate',
    assessmentId?: number | null,
  ) {
    if (isBlockCreationInProgress.value) return;

    const sourceBlockIndex = blockList.value.findIndex((block) => block.id === sourceBlockId);
    if (sourceBlockIndex === -1) {
      console.error('❌ Source header block not found for duplication:', sourceBlockId);
      return;
    }

    const sourceBlock = blockList.value[sourceBlockIndex];
    if (!sourceBlock || sourceBlock.type !== 'HEADER') {
      console.error('❌ Source block is not a header block:', sourceBlock);
      return;
    }

    try {
      isBlockCreationInProgress.value = true;
      isFabCreationInProgress.value = true;

      const globalStore = useGlobalStore();
      globalStore.startSaveOperation('Duplicating header (atomic operation)...');

      const finalAssessmentId = assessmentId || getAssessmentId();
      if (!validateAssessmentForOperation('header duplication')) {
        globalStore.completeSaveOperation(false, 'Assessment ID not found');
        return;
      }

      const currentSection = getCurrentSection(blockList.value, sourceBlockIndex);
      const assessmentService = new AssessmentService(assessmentType);

      const duplicatedBlock = await assessmentService.duplicateBlock(sourceBlockId, {
        assessmentId: finalAssessmentId!,
        sequence: sourceBlock.sequence + 1,
        section: currentSection,
      });

      if (duplicatedBlock) {
        await handleSuccessfulBlockDuplication(
          duplicatedBlock,
          sourceBlockIndex,
          assessmentType,
          globalStore,
        );
      } else {
        globalStore.completeSaveOperation(false, 'Failed to duplicate header');
      }
    } catch (error) {
      console.error('❌ Error during header block duplication:', error);
      const globalStore = useGlobalStore();
      globalStore.completeSaveOperation(false, 'Error duplicating header');
    } finally {
      isBlockCreationInProgress.value = false;
      cleanupBlockCreationState();
    }
  }

  async function handleSuccessfulBlockDuplication(
    duplicatedBlock: ItemBlock,
    sourceIndex: number,
    assessmentType: 'quiz' | 'evaluate',
    globalStore: ReturnType<typeof useGlobalStore>,
  ) {
    const duplicatedId = duplicatedBlock.id;

    // Insert the duplicated block immediately after the source block
    insertBlockAfterIndex(duplicatedBlock, sourceIndex);
    updateAllBlockSequences();

    // Update assessment if needed
    if (assessmentType === 'evaluate' && activeAssessment.value) {
      updateAssessmentWithNewBlock(duplicatedBlock, sourceIndex);
    }

    // Set FAB protection
    fabTargetBlockId.value = duplicatedId;
    isFabPositionLocked.value = true;

    await positionFabAndScrollToBlock(duplicatedId);
    currentSelectedBlockId.value = `block-${duplicatedId}`;

    const hasContent =
      duplicatedBlock.headerBody &&
      (duplicatedBlock.headerBody.title || duplicatedBlock.headerBody.description);

    globalStore.completeSaveOperation(
      true,
      hasContent
        ? 'Header duplicated successfully with content (atomic)'
        : 'Header duplicated successfully (atomic)',
    );

    setTimeout(() => {
      cleanupBlockCreationState();
      currentSelectedBlockId.value = `block-${duplicatedId}`;
    }, 300);
  }

  // ===== SECTION CREATION =====
  async function createNewSection(
    assessmentType: 'quiz' | 'evaluate',
    assessmentId?: number | null,
  ) {
    if (isBlockCreationInProgress.value) return;

    try {
      isBlockCreationInProgress.value = true;
      isFabCreationInProgress.value = true;

      const globalStore = useGlobalStore();
      globalStore.startSaveOperation('Creating new section...');

      const finalAssessmentId = assessmentId || getAssessmentId();
      if (!validateAssessmentForOperation('section creation')) {
        globalStore.completeSaveOperation(false, 'Assessment ID not found');
        return;
      }

      const newSectionNumber = findMaxSectionNumber() + 1;
      const newHeaderData = {
        assessmentId: finalAssessmentId!,
        sequence: blockList.value.length + 1,
        section: newSectionNumber,
        type: 'HEADER' as const,
        isRequired: false,
      };

      const assessmentService = new AssessmentService(assessmentType);
      const createdHeader = await assessmentService.createBlock(newHeaderData);

      if (createdHeader) {
        await handleSuccessfulSectionCreation(createdHeader, assessmentType, globalStore);
      } else {
        globalStore.completeSaveOperation(false, 'Failed to create section');
      }
    } catch (error) {
      console.error('❌ Error creating section:', error);
      const globalStore = useGlobalStore();
      globalStore.completeSaveOperation(false, 'Error creating section');
    } finally {
      isBlockCreationInProgress.value = false;
      cleanupBlockCreationState();
    }
  }

  async function handleSuccessfulSectionCreation(
    createdHeader: ItemBlock,
    assessmentType: 'quiz' | 'evaluate',
    globalStore: ReturnType<typeof useGlobalStore>,
  ) {
    const headerId = createdHeader.id;

    // Add to end of list
    addBlockToEnd(createdHeader);

    // Update assessment if needed
    if (assessmentType === 'evaluate' && activeAssessment.value) {
      const currentBlocks = activeAssessment.value.itemBlocks || [];
      const newAssessmentBlocks = [...currentBlocks, createdHeader];
      newAssessmentBlocks.forEach((block, idx) => {
        block.sequence = idx + 1;
      });
      activeAssessment.value.itemBlocks = newAssessmentBlocks;
    }

    await positionFabAndScrollToBlock(headerId);
    currentSelectedBlockId.value = `block-${headerId}`;
    globalStore.completeSaveOperation(true, 'Section created successfully');

    setTimeout(() => {
      cleanupBlockCreationState();
      currentSelectedBlockId.value = `block-${headerId}`;
    }, 800);
  }

  // ===== ASSESSMENT MANAGEMENT FUNCTIONS =====
  async function fetchAssessmentById(id: number) {
    isLoadingAssessment.value = true;
    assessmentError.value = null;
    try {
      const response = await new AssessmentService('evaluate').fetchOne(id);
      if (response) {
        activeAssessment.value = response;
        if (response.itemBlocks) {
          blockList.value = response.itemBlocks;
        }
      }
    } catch (err: unknown) {
      assessmentError.value = err instanceof Error ? err.message : 'ไม่สามารถโหลดแบบทดสอบได้';
    } finally {
      isLoadingAssessment.value = false;
    }
  }

  async function addAssessment(assessmentData: Partial<Assessment>): Promise<Assessment> {
    const response = await new AssessmentService('evaluate').createOne(assessmentData);
    assessmentCollection.value.push(response);
    activeAssessment.value = response;
    return response;
  }

  async function updateAssessment(id: number, assessmentData: Assessment): Promise<Assessment> {
    const response = await new AssessmentService('evaluate').updateOne(id, assessmentData);
    const index = assessmentCollection.value.findIndex((assessment) => assessment.id === id);
    if (index !== -1) assessmentCollection.value[index] = response;
    if (activeAssessment.value?.id === id) activeAssessment.value = response;
    return response;
  }

  async function removeAssessment(id: number): Promise<void> {
    await new AssessmentService('evaluate').deleteOne(id);
    assessmentCollection.value = assessmentCollection.value.filter(
      (assessment) => assessment.id !== id,
    );
    if (activeAssessment.value?.id === id) {
      activeAssessment.value = null;
    }
  }

  // ===== VALIDATION FUNCTIONS =====
  function validateAssessmentIds(): { valid: boolean; missing: string[] } {
    const missing: string[] = [];

    if (!activeAssessment.value?.id) {
      missing.push('assessmentId');
    }

    if (!activeAssessment.value?.itemBlocks || activeAssessment.value.itemBlocks.length === 0) {
      missing.push('itemBlocks');
    } else {
      activeAssessment.value.itemBlocks.forEach((block, index) => {
        if (!block.id) {
          missing.push(`itemBlock[${index}].id`);
        }
        if (!block.assessmentId) {
          missing.push(`itemBlock[${index}].assessmentId`);
        }
      });
    }

    return {
      valid: missing.length === 0,
      missing,
    };
  }

  function validateBlockDeletion(blockId: number): { canDelete: boolean; issues: string[] } {
    const issues: string[] = [];

    if (!activeAssessment.value) {
      issues.push('No current assessment loaded');
      return { canDelete: false, issues };
    }

    if (!blockId) {
      issues.push('Invalid block ID provided');
      return { canDelete: false, issues };
    }

    const targetBlock = activeAssessment.value.itemBlocks?.find((block) => block.id === blockId);
    if (!targetBlock) {
      issues.push(`Block with ID ${blockId} not found in current assessment`);
      return { canDelete: false, issues };
    }

    if (targetBlock.type === 'HEADER' && !targetBlock.headerBody) {
      issues.push('Header block missing headerBody data');
    }

    if (targetBlock.assessmentId !== activeAssessment.value.id) {
      issues.push(
        `Block assessmentId (${targetBlock.assessmentId}) does not match current assessment ID (${activeAssessment.value.id})`,
      );
    }

    return {
      canDelete: issues.length === 0,
      issues,
    };
  }

  function validatePostDeletion(deletedBlockId: number): { success: boolean; issues: string[] } {
    const issues: string[] = [];

    if (!activeAssessment.value) {
      issues.push('No current assessment loaded');
      return { success: false, issues };
    }

    const blockStillExists = activeAssessment.value.itemBlocks?.some(
      (block) => block.id === deletedBlockId,
    );
    if (blockStillExists) {
      issues.push(`Block with ID ${deletedBlockId} still exists in assessment after deletion`);
    }

    const orphanedQuestions = activeAssessment.value.itemBlocks?.some((block) =>
      block.questions?.some((question) => question.itemBlockId === deletedBlockId),
    );
    if (orphanedQuestions) {
      issues.push(`Found orphaned questions referencing deleted block ID ${deletedBlockId}`);
    }

    const orphanedOptions = activeAssessment.value.itemBlocks?.some((block) =>
      block.options?.some((option) => option.itemBlockId === deletedBlockId),
    );
    if (orphanedOptions) {
      issues.push(`Found orphaned options referencing deleted block ID ${deletedBlockId}`);
    }

    return {
      success: issues.length === 0,
      issues,
    };
  }

  // ===== STORE API EXPORT =====
  return {
    // ===== BLOCK MANAGEMENT STATE (with backward compatibility) =====
    blocks: blockList,
    selectedBlock: currentSelectedBlock,
    selectedBlockId: currentSelectedBlockId,

    // ===== ASSESSMENT MANAGEMENT STATE (with backward compatibility) =====
    assessments: assessmentCollection,
    currentAssessment: activeAssessment,
    loading: isLoadingAssessment,
    error: assessmentError,
    meta: assessmentMetadata,
    page: paginationPage,
    limit: paginationLimit,
    search: searchQuery,

    // ===== UI INTERACTION STATE (with backward compatibility) =====
    duplicateDialog: showDuplicateDialog,
    isCreatingBlock: isBlockCreationInProgress,
    isDragging: isDragOperationActive,
    forceUpdateTrigger: uiRefreshTrigger,

    // ===== FAB STATE (with backward compatibility) =====
    fabState: fabPositioningState,
    fabPositionLock: isFabPositionLocked,
    pendingFabPosition: pendingFabTargetPosition,
    blockCreationInProgress: isFabCreationInProgress,
    targetBlockId: fabTargetBlockId,

    // ===== TIMEOUT MANAGEMENT =====
    timeoutManager: fabTimeoutManager,

    // ===== BLOCK OPERATIONS (with backward compatibility) =====
    addBlock: insertBlockAfterIndex,
    appendBlock: addBlockToEnd,
    updateBlocksOrder: reorderBlocksAndUpdateSequences,
    setSection: updateBlockSection,
    updateBlock: replaceBlockAtIndex,
    deleteBlock: removeBlockFromList,
    duplicateBlock: createDuplicateBlock,

    // ===== BLOCK CREATION METHODS (with backward compatibility) =====
    handleAddBlockAfter: createNewBlockAfterIndex,
    handleAddHeaderAfter: createNewHeaderAfterIndex,
    handleDuplicateHeaderBlock: duplicateHeaderBlockAtomically,
    handleAddSection: createNewSection,

    // ===== FAB AND SCROLL MANAGEMENT (with backward compatibility) =====
    setFabPosition: updateFabPosition,
    createFabProtection: createFabProtectionForBlock,
    scrollToTarget: scrollToSelectedBlock,
    setFabAndScroll: positionFabAndScrollToBlock,

    // ===== UI STATE MANAGEMENT (with backward compatibility) =====
    forceRefreshBlocks: executeBasicUIRefresh,
    syncBlocksWithAssessment: synchronizeBlocksWithAssessment,

    // ===== BLOCK REFERENCE MANAGEMENT (with backward compatibility) =====
    setBlockRef: setBlockDOMReference,
    getBlockRef: getBlockDOMReference,

    // ===== BLOCK UTILITIES =====
    isAnswerItemBlockType,
    isSectionBlock,
    totalSections,
    getSectionNumber,
    resetBlocks,
    initializeBlocks,
    getAssessmentData,

    // ===== ID GENERATION (with backward compatibility) =====
    generateQuestionId: generateNextQuestionId,
    generateOptionId: generateNextOptionId,

    // ===== ASSESSMENT MANAGEMENT =====
    fetchAssessmentById,
    addAssessment,
    updateAssessment,
    removeAssessment,

    // ===== ID TRACKING HELPERS =====
    getAssessmentId,
    getItemBlockById,
    getHeaderBlockId,
    getRadioBlockId,
    getAllItemBlockIds,

    // ===== VALIDATION HELPERS =====
    validateIds: validateAssessmentIds,
    validateBlockDeletion,
    validatePostDeletion,
  };
});
