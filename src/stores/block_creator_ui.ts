import { defineStore } from 'pinia';
import { type ComponentPublicInstance } from 'vue';
import type { ItemBlock, Option } from 'src/types/models';
import { useBlockCreatorStore } from './block_creator';

type DOMRefElement = Element | ComponentPublicInstance | null;

export const useBlockCreatorUIStore = defineStore('blockCreatorUI', () => {
  // Block refs for scrolling/focus with caching for better performance
  const blockRefsCache = new Map<number, DOMRefElement>();

  // Scroll timeout for debouncing
  let scrollTimeout: NodeJS.Timeout | null = null;

  // Block refs proxy for optimized access
  const blockRefs: Record<number, DOMRefElement> = new Proxy(
    {},
    {
      get(_target, id: string | symbol) {
        // Handle Symbol keys (used by Vue's reactivity system)
        if (typeof id === 'symbol') {
          return undefined;
        }

        const numId = Number(id);
        // Handle invalid numeric conversions
        if (isNaN(numId)) {
          return undefined;
        }

        if (blockRefsCache.has(numId)) {
          return blockRefsCache.get(numId);
        }
        const blockCreatorStore = useBlockCreatorStore();
        const ref = blockCreatorStore.getBlockRef(numId) || null;
        blockRefsCache.set(numId, ref);
        return ref;
      },
      set(_target, id: string | symbol, el) {
        // Handle Symbol keys (used by Vue's reactivity system)
        if (typeof id === 'symbol') {
          return true;
        }

        const numId = Number(id);
        // Handle invalid numeric conversions
        if (isNaN(numId)) {
          return true;
        }

        blockRefsCache.set(numId, el);
        const blockCreatorStore = useBlockCreatorStore();
        blockCreatorStore.setBlockRef(numId, el);
        return true;
      },
    },
  );

  // Handle focus FAB events with aggressive blocking
  const handleFocusFab = (blockId: number) => {
    const blockCreatorStore = useBlockCreatorStore();

    // ABSOLUTE BLOCK: During block creation, REJECT ALL events except target
    if (blockCreatorStore.blockCreationInProgress) {
      if (blockCreatorStore.targetBlockId && blockId !== blockCreatorStore.targetBlockId) {
        // Force back to correct position immediately
        blockCreatorStore.selectedBlockId = `block-${blockCreatorStore.targetBlockId}`;
        return;
      }
    }

    // ABSOLUTE BLOCK: Don't allow ANY repositioning if locked
    if (blockCreatorStore.fabPositionLock) {
      return;
    }

    // Only allow positioning for legitimate user interactions
    blockCreatorStore.setFabPosition(blockId, false);
  };

  // Handle question updates from ItemBlockComponent
  const handleQuestionUpdate = (updateData: {
    questionId?: number;
    questionText?: string;
    itemBlockId: number;
    updatedQuestion?: object;
    updatedBlock?: ItemBlock;
    typeChanged?: boolean;
  }) => {
    const blockCreatorStore = useBlockCreatorStore();

    // Handle type changes
    if (updateData.typeChanged && updateData.updatedBlock) {
      // PROTECT FAB BEFORE DOM UPDATES
      blockCreatorStore.createFabProtection(updateData.itemBlockId);

      // Update the block type in the local store
      const blockIndex = blockCreatorStore.blocks.findIndex(
        (block) => block.id === updateData.itemBlockId,
      );
      if (blockIndex !== -1) {
        // Replace the entire block with the updated one from backend
        blockCreatorStore.blocks[blockIndex] = updateData.updatedBlock;
      }

      // Update the current assessment if this is an evaluate type
      if (blockCreatorStore.currentAssessment?.itemBlocks) {
        const assessmentBlockIndex = blockCreatorStore.currentAssessment.itemBlocks.findIndex(
          (block) => block.id === updateData.itemBlockId,
        );
        if (assessmentBlockIndex !== -1) {
          // Replace the entire block with the updated one from backend
          blockCreatorStore.currentAssessment.itemBlocks[assessmentBlockIndex] =
            updateData.updatedBlock;
        }
      }
      return;
    }

    // Handle question text updates (existing logic)
    if (updateData.questionId && updateData.questionText !== undefined) {
      // PROTECT FAB BEFORE DOM UPDATES
      blockCreatorStore.createFabProtection(updateData.itemBlockId);

      // Update the question in the local store
      const blockIndex = blockCreatorStore.blocks.findIndex(
        (block) => block.id === updateData.itemBlockId,
      );
      if (blockIndex !== -1) {
        const block = blockCreatorStore.blocks[blockIndex];
        if (block?.questions && block.questions.length > 0) {
          const questionIndex = block.questions.findIndex((q) => q.id === updateData.questionId);
          if (questionIndex !== -1 && block.questions[questionIndex]) {
            // Update the question text in the store
            block.questions[questionIndex].questionText = updateData.questionText;
          }
        }
      }

      // Update the current assessment if this is an evaluate type
      if (blockCreatorStore.currentAssessment?.itemBlocks) {
        const assessmentBlockIndex = blockCreatorStore.currentAssessment.itemBlocks.findIndex(
          (block) => block.id === updateData.itemBlockId,
        );
        if (assessmentBlockIndex !== -1) {
          const assessmentBlock =
            blockCreatorStore.currentAssessment.itemBlocks[assessmentBlockIndex];
          if (assessmentBlock?.questions && assessmentBlock.questions.length > 0) {
            const questionIndex = assessmentBlock.questions.findIndex(
              (q) => q.id === updateData.questionId,
            );
            if (questionIndex !== -1 && assessmentBlock.questions[questionIndex]) {
              // Update the question text in the current assessment
              assessmentBlock.questions[questionIndex].questionText = updateData.questionText;
            }
          }
        }
      }
    }
  };

  // Handle option updates from ItemBlockComponent
  const handleOptionUpdate = (updateData: {
    action: 'created' | 'updated';
    itemBlockId: number;
    option?: Option;
    optionId?: number;
    updateData?: { index: number; option: Option };
  }) => {
    const blockCreatorStore = useBlockCreatorStore();

    // PROTECT FAB BEFORE ANY DOM UPDATES
    blockCreatorStore.createFabProtection(updateData.itemBlockId);

    // Find the block in the local store
    const blockIndex = blockCreatorStore.blocks.findIndex(
      (block) => block.id === updateData.itemBlockId,
    );
    if (blockIndex === -1) {
      console.error('❌ Block not found for option update:', updateData.itemBlockId);
      return;
    }

    const block = blockCreatorStore.blocks[blockIndex];
    if (!block) {
      console.error('❌ Block is undefined:', updateData.itemBlockId);
      return;
    }

    // Handle option creation
    if (updateData.action === 'created' && updateData.option) {
      // Initialize options array if it doesn't exist
      if (!block.options) {
        block.options = [];
      }

      // Add the new option to the block
      block.options.push(updateData.option);

      // Update the current assessment if this is an evaluate type
      if (blockCreatorStore.currentAssessment?.itemBlocks) {
        const assessmentBlockIndex = blockCreatorStore.currentAssessment.itemBlocks.findIndex(
          (assessmentBlock) => assessmentBlock.id === updateData.itemBlockId,
        );
        if (assessmentBlockIndex !== -1) {
          const assessmentBlock =
            blockCreatorStore.currentAssessment.itemBlocks[assessmentBlockIndex];
          if (assessmentBlock) {
            // Initialize options array if it doesn't exist
            if (!assessmentBlock.options) {
              assessmentBlock.options = [];
            }
            // Add the new option to the assessment block
            assessmentBlock.options.push(updateData.option);
          }
        }
      }
    }

    // Handle option updates
    if (updateData.action === 'updated' && updateData.optionId && updateData.updateData) {
      const { index, option } = updateData.updateData;

      // Update the option in the local store
      if (block.options && block.options[index]) {
        block.options[index] = option;
      }

      // Update the current assessment if this is an evaluate type
      if (blockCreatorStore.currentAssessment?.itemBlocks) {
        const assessmentBlockIndex = blockCreatorStore.currentAssessment.itemBlocks.findIndex(
          (assessmentBlock) => assessmentBlock.id === updateData.itemBlockId,
        );
        if (assessmentBlockIndex !== -1) {
          const assessmentBlock =
            blockCreatorStore.currentAssessment.itemBlocks[assessmentBlockIndex];
          if (assessmentBlock?.options && assessmentBlock.options[index]) {
            // Update the option in the assessment block
            assessmentBlock.options[index] = option;
          }
        }
      }
    }
  };

  // Handle isRequired updates from ItemBlockComponent
  const handleIsRequiredUpdate = async (updateData: {
    itemBlockId: number;
    isRequired: boolean;
  }) => {
    const blockCreatorStore = useBlockCreatorStore();

    // PROTECT FAB BEFORE ANY DOM UPDATES
    blockCreatorStore.createFabProtection(updateData.itemBlockId);

    // Find the block in the local store
    const blockIndex = blockCreatorStore.blocks.findIndex(
      (block) => block.id === updateData.itemBlockId,
    );
    if (blockIndex === -1) {
      console.error('❌ Block not found for isRequired update:', updateData.itemBlockId);
      return;
    }

    const block = blockCreatorStore.blocks[blockIndex];
    if (!block) {
      console.error('❌ Block is undefined:', updateData.itemBlockId);
      return;
    }

    try {
      // Update the isRequired property in the local store first (optimistic update)
      const updatedBlock = {
        ...block,
        isRequired: Boolean(updateData.isRequired),
      };
      blockCreatorStore.updateBlock(updatedBlock, blockIndex);

      // Update the current assessment if this is an evaluate type
      if (blockCreatorStore.currentAssessment?.itemBlocks) {
        const assessmentBlockIndex = blockCreatorStore.currentAssessment.itemBlocks.findIndex(
          (assessmentBlock) => assessmentBlock.id === updateData.itemBlockId,
        );
        if (assessmentBlockIndex !== -1) {
          const assessmentBlock =
            blockCreatorStore.currentAssessment.itemBlocks[assessmentBlockIndex];
          if (assessmentBlock) {
            // Update the isRequired property in the assessment block with proper boolean value
            assessmentBlock.isRequired = Boolean(updateData.isRequired);

            // Trigger reactivity by creating a new array reference
            // This ensures the watcher in EvaluateSettingView detects the change
            blockCreatorStore.currentAssessment.itemBlocks = [
              ...blockCreatorStore.currentAssessment.itemBlocks,
            ];
          }
        }
      }

      // Call the backend API to persist the change
      const assessmentService = new (
        await import('src/services/asm/assessmentService')
      ).AssessmentService('evaluate');
      const apiUpdatedBlock = await assessmentService.updateBlock(updatedBlock);

      if (apiUpdatedBlock) {
        // Update the local store with the response from the API (in case there are any differences)
        blockCreatorStore.updateBlock(apiUpdatedBlock, blockIndex);

        // Update the current assessment with the API response
        if (blockCreatorStore.currentAssessment?.itemBlocks) {
          const assessmentBlockIndex = blockCreatorStore.currentAssessment.itemBlocks.findIndex(
            (assessmentBlock) => assessmentBlock.id === updateData.itemBlockId,
          );
          if (assessmentBlockIndex !== -1) {
            blockCreatorStore.currentAssessment.itemBlocks[assessmentBlockIndex] = apiUpdatedBlock;
            // Trigger reactivity
            blockCreatorStore.currentAssessment.itemBlocks = [
              ...blockCreatorStore.currentAssessment.itemBlocks,
            ];
          }
        }
      }
    } catch (error) {
      console.error(`❌ Failed to update isRequired for block ${updateData.itemBlockId}:`, error);

      // Revert the optimistic update on error
      blockCreatorStore.updateBlock(block, blockIndex);

      if (blockCreatorStore.currentAssessment?.itemBlocks) {
        const assessmentBlockIndex = blockCreatorStore.currentAssessment.itemBlocks.findIndex(
          (assessmentBlock) => assessmentBlock.id === updateData.itemBlockId,
        );
        if (assessmentBlockIndex !== -1) {
          const assessmentBlock =
            blockCreatorStore.currentAssessment.itemBlocks[assessmentBlockIndex];
          if (assessmentBlock) {
            assessmentBlock.isRequired = block.isRequired;
            // Trigger reactivity
            blockCreatorStore.currentAssessment.itemBlocks = [
              ...blockCreatorStore.currentAssessment.itemBlocks,
            ];
          }
        }
      }

      // Error notification removed
    }
  };

  // Cleanup function
  const cleanup = () => {
    const blockCreatorStore = useBlockCreatorStore();
    blockCreatorStore.timeoutManager.clearAll();
    if (scrollTimeout) {
      clearTimeout(scrollTimeout);
      scrollTimeout = null;
    }
    // Clear block refs cache
    blockRefsCache.clear();
  };

  return {
    // Block refs management
    blockRefs,
    blockRefsCache,

    // Event handlers
    handleFocusFab,
    handleQuestionUpdate,
    handleOptionUpdate,
    handleIsRequiredUpdate,

    // Utility functions
    cleanup,
  };
});
