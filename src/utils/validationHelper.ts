import type { ItemBlock, Assessment } from 'src/types/models';

export interface ValidationResult {
  valid: boolean;
  missing: string[];
}

export interface BlockDeletionValidation {
  canDelete: boolean;
  issues: string[];
}

export interface PostDeletionValidation {
  success: boolean;
  issues: string[];
}

/**
 * Validates that all required IDs are present in the assessment
 */
export function validateAssessmentIds(
  currentAssessment: Assessment | null
): ValidationResult {
  const missing: string[] = [];

  if (!currentAssessment?.id) {
    missing.push('assessmentId');
  }

  if (!currentAssessment?.itemBlocks || currentAssessment.itemBlocks.length === 0) {
    missing.push('itemBlocks');
  } else {
    currentAssessment.itemBlocks.forEach((block, index) => {
      if (!block.id) {
        missing.push(`itemBlock[${index}].id`);
      }
      if (!block.assessmentId) {
        missing.push(`itemBlock[${index}].assessmentId`);
      }
    });
  }

  return {
    valid: missing.length === 0,
    missing,
  };
}

/**
 * Validates whether a block can be safely deleted
 */
export function validateBlockForDeletion(
  blockId: number,
  currentAssessment: Assessment | null
): BlockDeletionValidation {
  const issues: string[] = [];

  if (!currentAssessment) {
    issues.push('No current assessment loaded');
    return { canDelete: false, issues };
  }

  if (!blockId) {
    issues.push('Invalid block ID provided');
    return { canDelete: false, issues };
  }

  const targetBlock = currentAssessment.itemBlocks?.find((block) => block.id === blockId);
  if (!targetBlock) {
    issues.push(`Block with ID ${blockId} not found in current assessment`);
    return { canDelete: false, issues };
  }

  // Additional validation for header blocks
  if (targetBlock.type === 'HEADER' && !targetBlock.headerBody) {
    issues.push('Header block missing headerBody data');
  }

  // Check for orphaned references
  if (targetBlock.assessmentId !== currentAssessment.id) {
    issues.push(
      `Block assessmentId (${targetBlock.assessmentId}) does not match current assessment ID (${currentAssessment.id})`
    );
  }

  return {
    canDelete: issues.length === 0,
    issues,
  };
}

/**
 * Validates the state after a block deletion
 */
export function validatePostDeletionState(
  deletedBlockId: number,
  currentAssessment: Assessment | null
): PostDeletionValidation {
  const issues: string[] = [];

  if (!currentAssessment) {
    issues.push('No current assessment loaded');
    return { success: false, issues };
  }

  // Check if the block still exists in the assessment
  const blockStillExists = currentAssessment.itemBlocks?.some(
    (block) => block.id === deletedBlockId
  );
  if (blockStillExists) {
    issues.push(`Block with ID ${deletedBlockId} still exists in assessment after deletion`);
  }

  // Check for any orphaned references
  const orphanedQuestions = currentAssessment.itemBlocks?.some((block) =>
    block.questions?.some((question) => question.itemBlockId === deletedBlockId)
  );
  if (orphanedQuestions) {
    issues.push(`Found orphaned questions referencing deleted block ID ${deletedBlockId}`);
  }

  const orphanedOptions = currentAssessment.itemBlocks?.some((block) =>
    block.options?.some((option) => option.itemBlockId === deletedBlockId)
  );
  if (orphanedOptions) {
    issues.push(`Found orphaned options referencing deleted block ID ${deletedBlockId}`);
  }

  return {
    success: issues.length === 0,
    issues,
  };
}

/**
 * Validates assessment ID availability for operations
 */
export function validateAssessmentIdForOperation(
  assessmentId: number | null | undefined,
  operationName: string
): { valid: boolean; error?: string } {
  if (!assessmentId) {
    return {
      valid: false,
      error: `Assessment ID is required for ${operationName}`,
    };
  }

  return { valid: true };
}

/**
 * Validates block data before creation
 */
export function validateBlockCreationData(data: {
  assessmentId?: number | null;
  sequence?: number;
  section?: number;
  type?: string;
}): { valid: boolean; issues: string[] } {
  const issues: string[] = [];

  if (!data.assessmentId) {
    issues.push('assessmentId is required');
  }

  if (data.sequence !== undefined && data.sequence < 1) {
    issues.push('sequence must be greater than 0');
  }

  if (data.section !== undefined && data.section < 1) {
    issues.push('section must be greater than 0');
  }

  if (!data.type) {
    issues.push('type is required');
  }

  return {
    valid: issues.length === 0,
    issues,
  };
}

/**
 * Validates that blocks array is in a consistent state
 */
export function validateBlocksConsistency(blocks: ItemBlock[]): {
  valid: boolean;
  issues: string[];
} {
  const issues: string[] = [];

  // Check for duplicate IDs
  const ids = blocks.map(block => block.id).filter(id => id !== undefined);
  const uniqueIds = new Set(ids);
  if (ids.length !== uniqueIds.size) {
    issues.push('Duplicate block IDs found');
  }

  // Check sequence consistency
  const sequences = blocks.map(block => block.sequence).sort((a, b) => a - b);
  for (let i = 0; i < sequences.length; i++) {
    if (sequences[i] !== i + 1) {
      issues.push(`Sequence gap or duplicate found at position ${i + 1}`);
      break;
    }
  }

  // Check section consistency
  const headerBlocks = blocks.filter(block => block.type === 'HEADER');
  const sections = headerBlocks.map(block => block.section).sort((a, b) => a - b);
  for (let i = 0; i < sections.length; i++) {
    if (sections[i] !== i + 1) {
      issues.push(`Section gap or duplicate found at section ${i + 1}`);
      break;
    }
  }

  return {
    valid: issues.length === 0,
    issues,
  };
}
