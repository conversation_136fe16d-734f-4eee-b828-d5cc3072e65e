import { blockBodyOptions } from 'src/data/blocks';
import type { BlockBodyOptionsType } from 'src/types/app';
import type { ItemBlock, Option } from 'src/types/models';

export const extractBlockBodyType = (itemBlock: ItemBlock): BlockBodyOptionsType => {
  return blockBodyOptions.find((option) => option.value === itemBlock.type) || blockBodyOptions[0]!;
};

/**
 * Helper function to determine the correct section for a new block
 * @param blocks - Array of ItemBlocks to search through
 * @param index - Index position to determine section for
 * @returns Section number for the new block
 */
export const getCurrentSection = (blocks: ItemBlock[], index: number): number => {
  // Find the section of the block at the given index
  const currentBlock = blocks[index];
  if (currentBlock) {
    return currentBlock.section;
  }

  // If no current block, find the section of the nearest previous block
  for (let i = index - 1; i >= 0; i--) {
    const block = blocks[i];
    if (block) {
      return block.section;
    }
  }

  // Default to section 1 if no blocks found
  return 1;
};

/**
 * ATOMIC DUPLICATION: Creates a complete copy of an ItemBlock using backend atomic operation
 * This completely eliminates the race condition by using a single backend API call
 */
export const duplicateBlockAtomic = async (
  sourceBlockId: number,
  duplicateData: { assessmentId: number; sequence?: number; section?: number },
  assessmentService: {
    duplicateBlock: (
      sourceBlockId: number,
      duplicateData: { assessmentId: number; sequence?: number; section?: number },
    ) => Promise<ItemBlock | undefined>;
  },
): Promise<ItemBlock | undefined> => {
  try {
    console.log('🔄 Starting atomic block duplication:', {
      sourceBlockId,
      duplicateData,
      timestamp: new Date().toISOString(),
    });

    // Call the new atomic duplication API
    const duplicatedBlock = await assessmentService.duplicateBlock(sourceBlockId, duplicateData);

    if (duplicatedBlock) {
      console.log('✅ Atomic duplication completed successfully:', {
        sourceBlockId,
        duplicatedBlockId: duplicatedBlock.id,
        hasContent: !!(
          duplicatedBlock.headerBody ||
          duplicatedBlock.imageBody ||
          (duplicatedBlock.questions && duplicatedBlock.questions.length > 0) ||
          (duplicatedBlock.options && duplicatedBlock.options.length > 0)
        ),
        timestamp: new Date().toISOString(),
      });

      return duplicatedBlock;
    } else {
      console.error('❌ Atomic duplication failed: No block returned from backend');
      return undefined;
    }
  } catch (error) {
    console.error('❌ Atomic duplication error:', error);
    throw error;
  }
};

/**
 * LEGACY FRONTEND-ONLY FIX: Enhanced block content copying with race condition prevention
 * - Increased delays to ensure backend operations complete before UI updates
 * - Uses deep cloning to prevent reference sharing between source and target blocks
 * - Provides atomic duplication from frontend perspective
 *
 * @deprecated Use duplicateBlockAtomic instead for better performance and reliability
 */
export const copyBlockContentWithBackendPersistence = async (
  source: ItemBlock,
  target: ItemBlock,
): Promise<ItemBlock> => {
  try {
    // Import OptionService for creating options with backend persistence
    const { OptionService } = await import('src/services/asm/optionService');
    const optionService = new OptionService();

    // FRONTEND FIX: Wait longer to ensure backend has finished creating default options
    // This prevents race conditions where default options are created after we start
    await new Promise((resolve) => setTimeout(resolve, 300));

    // FRONTEND FIX: Create a deep copy of the target block to avoid mutations and reference sharing
    const updatedTarget = structuredClone
      ? structuredClone(target)
      : JSON.parse(JSON.stringify(target));

    // For ImageBlocks, copy the imageBody contents
    if (source.type === 'IMAGE' && source.imageBody && target.imageBody) {
      try {
        // Import ImageBodyService dynamically
        const { ImageBodyService } = await import('src/services/asm/imageBodyService');
        const imageBodyService = new ImageBodyService();

        // Extract relative path from imagePath (if it exists)
        let imagePathToUse = source.imageBody.imagePath;
        if (imagePathToUse && imagePathToUse.includes('uploaded_files/')) {
          // Extract only the relative path (no signed URL, no query params)
          const match = imagePathToUse.match(/uploaded_files\/[^?]+/);
          if (match) {
            imagePathToUse = match[0];
          }
        }

        // Update the target imageBody with source content
        const updatedImageBody = await imageBodyService.updateImageBody(
          target.imageBody.id,
          {
            imagePath: imagePathToUse ?? '',
            imageText: source.imageBody.imageText || '',
            ...(source.imageBody.imageWidth !== undefined
              ? { imageWidth: source.imageBody.imageWidth }
              : {}),
            ...(source.imageBody.imageHeight !== undefined
              ? { imageHeight: source.imageBody.imageHeight }
              : {}),
            itemBlockId: target.id,
          },
          // No file parameter since we're copying an existing image path
        );

        if (updatedImageBody) {
          updatedTarget.imageBody = updatedImageBody;
        }
      } catch {
        // Continue with the rest of the duplication even if imageBody copy fails
      }
    }

    // Copy and update questions if source has questions
    if (source.questions && source.questions.length > 0 && updatedTarget.questions) {
      const updatedQuestions = [];

      // Update existing questions with source content via backend API
      for (let index = 0; index < updatedTarget.questions.length; index++) {
        const targetQuestion = updatedTarget.questions[index];
        const sourceQuestion = source.questions[index];

        if (sourceQuestion && targetQuestion) {
          try {
            // Import QuestionService dynamically
            const questionServiceModule = await import('src/services/asm/questionService');
            const questionService = questionServiceModule.default;

            // --- Fix: Always preserve relative imagePath if present ---
            let imagePathToUse = sourceQuestion.imagePath;
            if (imagePathToUse && imagePathToUse.includes('uploaded_files/')) {
              // Extract only the relative path (no signed URL, no query params)
              const match = imagePathToUse.match(/uploaded_files\/[^?]+/);
              if (match) {
                imagePathToUse = match[0];
              }
            }

            // Prepare question update data
            const questionUpdateData = {
              questionText: sourceQuestion.questionText || '', // ✅ Preserve original questionText
              isHeader: sourceQuestion.isHeader,
              sequence: sourceQuestion.sequence,
              score: sourceQuestion.score || 0,
              itemBlockId: target.id,
              // Copy optional properties if they exist
              ...(imagePathToUse && { imagePath: imagePathToUse }),
              ...(sourceQuestion.imageWidth && { imageWidth: sourceQuestion.imageWidth }),
              ...(sourceQuestion.imageHeight && { imageHeight: sourceQuestion.imageHeight }),
              ...(sourceQuestion.sizeLimit && { sizeLimit: sourceQuestion.sizeLimit }),
              ...(sourceQuestion.acceptFile && { acceptFile: sourceQuestion.acceptFile }),
              ...(sourceQuestion.uploadLimit && { uploadLimit: sourceQuestion.uploadLimit }),
            };

            const updatedQuestion = await questionService.updateQuestion(
              targetQuestion.id,
              questionUpdateData,
            );

            if (updatedQuestion) {
              updatedQuestions.push(updatedQuestion);
            }
          } catch {
            // Fallback to original question if update fails
            updatedQuestions.push(targetQuestion);
          }
        } else if (targetQuestion) {
          // Keep original question if no corresponding source question
          updatedQuestions.push(targetQuestion);
        }
      }

      // Update the target with the backend-updated questions
      if (updatedQuestions.length > 0) {
        updatedTarget.questions = updatedQuestions;
      }
    }

    // Copy and create options if source has options
    if (source.options && source.options.length > 0) {
      const createdOptions: Option[] = [];

      // First, ensure we have a clean slate by removing ALL existing options
      if (target.options && target.options.length > 0) {
        // Create array of deletion promises to handle them concurrently but safely
        const deletionPromises = target.options.map(async (existingOption) => {
          try {
            await optionService.removeOption(existingOption.id);
            return { success: true, id: existingOption.id };
          } catch {
            return { success: false, id: existingOption.id };
          }
        });

        // Wait for all deletions to complete
        await Promise.all(deletionPromises);

        // Clear the target options array to ensure clean state
        updatedTarget.options = [];
      }

      // Wait a brief moment to ensure deletions are processed
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Then create new options based on source (preserving exact count and optionText)
      for (let i = 0; i < source.options.length; i++) {
        const sourceOption = source.options[i];

        // Skip if sourceOption is undefined (safety check)
        if (!sourceOption) {
          continue;
        }

        try {
          const newOptionData = {
            optionText: sourceOption.optionText || '', // ✅ Preserve original optionText
            value: sourceOption.value || 0,
            sequence: sourceOption.sequence || i + 1, // Ensure proper sequence
            itemBlockId: target.id,
            ...(sourceOption.imagePath && { imagePath: sourceOption.imagePath }),
            ...(sourceOption.nextSection && { nextSection: sourceOption.nextSection }),
          };

          const createdOption = await optionService.createOption(newOptionData);

          if (createdOption) {
            createdOptions.push(createdOption);
          }
        } catch {
          // Continue with other options even if one fails
        }
      }

      // Verify we created the exact number of options as the source
      if (createdOptions.length === source.options.length) {
        updatedTarget.options = createdOptions;
      } else {
        // Still update with what we have, but log the discrepancy
        updatedTarget.options = createdOptions;
      }

      // Final safety check: Ensure no duplicate or unexpected options exist
      if (updatedTarget.options && updatedTarget.options.length > 0) {
        const uniqueIds = new Set(updatedTarget.options.map((opt: Option) => opt.id));
        if (uniqueIds.size !== updatedTarget.options.length) {
          console.error('❌ Duplicate option IDs detected in duplicated block!', {
            totalOptions: updatedTarget.options.length,
            uniqueIds: uniqueIds.size,
            optionIds: updatedTarget.options.map((opt: Option) => opt.id),
          });
        }
      }
    }

    return updatedTarget;
  } catch {
    // Return original target if copy fails
    return target;
  }
};
