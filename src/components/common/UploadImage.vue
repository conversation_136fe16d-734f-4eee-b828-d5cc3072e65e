<template>
  <q-dialog class="q-pa-md" v-model="dialogModel">
    <div
      class="q-pa-md"
      style="
        width: 90vw;
        max-width: 900px;
        height: auto;
        max-height: 90vh;
        overflow-y: auto;
        background-color: white;
      "
    >
      <div class="row items-center justify-between">
        <div class="text-h6 text-weight-bold">อัพโหลดรูปภาพ</div>
        <q-btn flat dense icon="close" size="lg" @click="close" />
      </div>
      <div style="height: 16px"></div>
      <q-separator />

      <!-- Drop Zone -->
      <div
        class="q-mt-md q-mb-md drop-zone q-pa-lg flex flex-center column"
        @dragover.prevent
        @drop.prevent="handleDrop"
      >
        <q-icon name="image" size="246px" color="grey-4" />
        <div class="upload-wrapper">
          <q-btn
            class="rounded-borders q-px-xl q-py-sm"
            label="เปิดโฟลเดอร์"
            no-caps
            text-color="white"
            style="
              border-radius: 12px;
              font-weight: 600;
              position: relative;
              min-width: 200px;
              background-color: #3d3c91;
            "
          >
            <q-file
              multiple
              v-model="imageFiles"
              accept="image/*"
              class="file-overlay"
              @update:model-value="handleFiles"
              data-cy="upload_image_btn"
            />
          </q-btn>
          <div class="text-subttile text-grey q-mt-sm">หรือลากไฟล์มาที่นี่</div>
        </div>
      </div>
    </div>
  </q-dialog>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useImageStore } from 'stores/image';

const props = defineProps<{
  modelValue: boolean;
  itemBlockId?: number | null;
  questionId?: number | null;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
  (e: 'image-uploaded'): void;
}>();

const dialogModel = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const imageFiles = ref<File[] | null>(null);
const imageStore = useImageStore();

// Utility function to extract image dimensions
async function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(file);

    img.onload = () => {
      // Clean up the object URL to prevent memory leaks
      URL.revokeObjectURL(url);
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight,
      });
    };

    img.onerror = () => {
      // Clean up the object URL on error
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load image for dimension extraction'));
    };

    img.src = url;
  });
}

async function handleFiles(files: File[] | null) {
  if (!files) {
    console.error('❌ Cannot upload image: missing files');
    return;
  }

  // Check if we have either itemBlockId for ImageBody or questionId for Question context
  if (!props.itemBlockId && !props.questionId) {
    console.error('❌ Cannot upload image: missing both itemBlockId and questionId');
    return;
  }

  try {
    for (const file of files) {
      try {
        // Extract image dimensions before uploading
        console.log('📏 Extracting image dimensions...');
        let dimensions: { width: number; height: number };

        try {
          dimensions = await getImageDimensions(file);
          console.log('✅ Image dimensions extracted:', {
            width: dimensions.width,
            height: dimensions.height,
            fileName: file.name,
            fileSize: file.size,
          });
        } catch (dimensionError) {
          console.warn('⚠️ Failed to extract image dimensions, using defaults:', dimensionError);
          // Use default dimensions if extraction fails
          dimensions = { width: 0, height: 0 };
        }

        // Check if this is a question context (questionId provided)
        if (props.questionId) {
          console.log(
            '🎯 Question context detected, using AssessmentService for questionId:',
            props.questionId,
          );

          // Import AssessmentService for question updates
          const { AssessmentService } = await import('src/services/asm/assessmentService');
          const assessmentService = new AssessmentService('evaluate'); // Default to 'evaluate' type

          // For question context, we need to upload the file first, then update the question
          // We'll use ImageBodyService to upload the file and get the path, then update the question
          const { ImageBodyService } = await import('src/services/asm/imageBodyService');
          const imageBodyService = new ImageBodyService();

          // Upload the file to get the image path
          const tempImageBody = await imageBodyService.createImageBody(
            {
              itemBlockId: props.itemBlockId || 0, // Use 0 as temp value if no itemBlockId
              imageWidth: dimensions.width,
              imageHeight: dimensions.height,
            },
            file,
          );

          // Ensure we got a valid image path from the upload
          if (!tempImageBody.imagePath) {
            console.error('❌ ImageBody upload failed: no imagePath returned');
            throw new Error('Failed to upload image: no path received');
          }

          // Now update the question with the image path and dimensions
          const updatedQuestion = await assessmentService.updateQuestion(props.questionId, {
            imagePath: tempImageBody.imagePath,
            imageWidth: dimensions.width,
            imageHeight: dimensions.height,
          });

          console.log('✅ Question image uploaded successfully:', {
            questionId: props.questionId,
            imagePath: tempImageBody.imagePath,
            dimensions: {
              width: dimensions.width,
              height: dimensions.height,
            },
            updatedQuestion,
          });
        } else if (props.itemBlockId) {
          console.log(
            '🎯 ItemBlock context detected, using ImageBodyService for itemBlockId:',
            props.itemBlockId,
          );

          // Original ImageBody logic for regular item blocks
          const { ImageBodyService } = await import('src/services/asm/imageBodyService');
          const imageBodyService = new ImageBodyService();

          // Since the backend automatically creates an empty ImageBody when creating an IMAGE ItemBlock,
          // we need to find the existing ImageBody and update it with the uploaded file
          let imageBody;

          // First, try to get the existing ImageBody for this ItemBlock
          const existingImageBody = await imageBodyService.getImageBodyByItemBlockId(
            props.itemBlockId,
          );

          if (existingImageBody) {
            // Update the existing ImageBody with the uploaded file, itemBlockId, and dimensions
            console.log('📝 Updating existing ImageBody:', existingImageBody.id);
            imageBody = await imageBodyService.updateImageBody(
              existingImageBody.id,
              {
                itemBlockId: props.itemBlockId,
                imageWidth: dimensions.width,
                imageHeight: dimensions.height,
                // Don't send imageText - let backend preserve existing value
              },
              file,
            );
          } else {
            // If no ImageBody exists (shouldn't happen for IMAGE ItemBlocks), create a new one
            console.log('🆕 Creating new ImageBody for ItemBlock:', props.itemBlockId);
            imageBody = await imageBodyService.createImageBody(
              {
                itemBlockId: props.itemBlockId,
                imageWidth: dimensions.width,
                imageHeight: dimensions.height,
                // Don't send imageText - let backend handle default value
              },
              file,
            );
          }

          console.log('✅ ImageBody uploaded successfully:', {
            imageBody,
            itemBlockId: props.itemBlockId,
            imageText: imageBody.imageText,
            imagePath: imageBody.imagePath,
            dimensions: {
              width: dimensions.width,
              height: dimensions.height,
              storedWidth: imageBody.imageWidth,
              storedHeight: imageBody.imageHeight,
            },
          });
        }

        // Store the image data in the image store for potential future use
        const reader = new FileReader();
        const result = await new Promise<string>((resolve, reject) => {
          reader.onload = (e) => {
            if (e.target?.result) {
              resolve(e.target.result as string);
            } else {
              reject(new Error('Failed to read file'));
            }
          };
          reader.onerror = () =>
            reject(new Error(reader.error?.message || 'Unknown file read error'));
          reader.readAsDataURL(file);
        });

        imageStore.imageData = result;

        // Set the uploaded image dimensions in the image store for FloatImageBtn
        imageStore.widthPixel = dimensions.width;
        imageStore.heightPixel = dimensions.height;

        console.log('🎯 Updated image store with uploaded dimensions:', {
          width: dimensions.width,
          height: dimensions.height,
          contextType: props.questionId ? 'question' : 'itemBlock',
          contextId: props.questionId || props.itemBlockId,
        });

        // Emit success and close dialog
        emit('image-uploaded');
        close();
        break; // Only handle the first file for now
      } catch (error) {
        console.error('❌ Failed to upload image:', error);
        // Continue to next file if multiple files were selected
      }
    }
  } catch (error) {
    console.error('❌ Failed to import required services:', error);
  }
}

function close() {
  dialogModel.value = false;
}

async function handleDrop(event: DragEvent) {
  const files = Array.from(event.dataTransfer?.files || []);
  const imageFilesOnly = files.filter((file) => file.type.startsWith('image/'));
  await handleFiles(imageFilesOnly);
}
</script>

<style scoped>
.drop-zone {
  border: 2px dashed #999;
  border-radius: 12px;
  text-align: center;
  min-height: 150px;
  background-color: #f9f9f9;
  transition: background-color 0.3s;
}
.drop-zone:hover {
  background-color: #f0f0f0;
}
.rounded-borders {
  border-radius: 8px;
  border: 1px solid #ccc;
}
.import-file {
  max-width: 300px;
}
.file-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.icon-responsive {
  width: 60vw;
  max-width: 240px;
  height: auto;
}
</style>
