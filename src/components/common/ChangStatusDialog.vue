<template>
  <q-dialog v-model="dialog" persistent>
    <q-card class="q-pa-lg q-mx-md" style="border-radius: 16px; width: 350px; max-width: 90vw">
      <q-card-section class="text-h6 text-center"> เปลี่ยนสถานะการเผยแพร่ </q-card-section>
      <!-- Card สถานะ 2 ฝั่ง -->
      <q-card-section>
        <div class="row justify-around">
          <div class="col-5">
            <!-- Card FALSE -->
            <q-card
              flat
              bordered
              class="text-center cursor-pointer"
              :class="!assStastatus ? 'bg-secondary text-white' : ''"
              @click="assStastatus = false"
            >
              <q-card-section>
                <q-icon name="cancel" size="24px" />
                <div class="text-subtitle1 q-mt-sm">ไม่เผยแพร่</div>
              </q-card-section>
            </q-card>
          </div>
          <!-- Card TRUE -->
          <div class="col-5">
            <q-card
              flat
              bordered
              class="text-center cursor-pointer"
              :class="assStastatus ? 'bg-positive text-white' : ''"
              @click="assStastatus = true"
            >
              <q-card-section>
                <q-icon name="check_circle" size="24px" />
                <div class="text-subtitle1 q-mt-sm">เผยแพร่อยู่</div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </q-card-section>

      <!-- ปุ่ม -->
      <q-card-actions align="right">
        <q-btn label="ยกเลิก" color="grey-4" class="text-black" @click="dialog = false" />
        <q-btn label="ยืนยัน" color="primary" class="text-black" @click="confirm" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { AssessmentService } from 'src/services/asm/assessmentService';
import type { AssessmentType } from 'src/types/data';
const dialog = ref(false);
const assId = ref(0);
const assStastatus = ref(false);
const confirmLabel = ref('ยืนยัน');
const path = ref<AssessmentType>('evaluate');
function openDialog(id: number, status: boolean, okStr: string, pathPrefix: string) {
  console.log(pathPrefix);
  console.log(status);

  assId.value = id;
  assStastatus.value = status;
  confirmLabel.value = okStr;
  path.value = pathPrefix as AssessmentType;
  dialog.value = true;
}

async function confirm() {
  console.log('ยืนยันแล้ว: ', assStastatus.value);
  await new AssessmentService(path.value).updateOne(assId.value, { status: assStastatus.value });
  dialog.value = false;
  window.location.reload();
}

defineExpose({ openDialog });
</script>
